<?php
/**
 *
 * Kirki color section
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */
function dmrthema_kirki_section_color( $wp_customize ) {

	// Colors.
	$wp_customize->add_section( 'dmrthema_color_section_topbar', array(
		'title'			 => esc_html__( 'Top Bar', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_header', array(
		'title'			 => esc_html__( 'Header', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_below_header', array(
		'title'			 => esc_html__( 'Below Header', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_navigation', array(
		'title'			 => esc_html__( 'Navigation', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_general', array(
		'title'			 => esc_html__( 'General', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_woocommerce', array(
		'title'			 => esc_html__( 'WooCommerce', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_color_section_footer', array(
		'title'			 => esc_html__( 'Footer', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_colors',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'dmrthema_kirki_section_color' );


