<?php
/**
 *
 * Kirki options panels
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */
function dmrthema_kirki_panels( $wp_customize ) {

	$wp_customize->add_panel( 'dmrthema_panel_general', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'General', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage general theme settings', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_colors', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Colors', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage theme colors', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_mainmenu', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Header and Navigation', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage the header and navigation', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_heading', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Page Heading', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage the page heading', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_typography', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Typography', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage theme typography', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_layout', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Layout', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage theme header, footer and more', 'dmrthema' ),
	) );
	$wp_customize->add_panel( 'dmrthema_panel_blog', array(
		'priority'		 => 10,
		'title'			 => esc_html__( 'Blog', 'dmrthema' ),
		'description'	 => esc_html__( 'Manage blog settings', 'dmrthema' ),
	) );
}

add_action( 'customize_register', 'dmrthema_kirki_panels' );


