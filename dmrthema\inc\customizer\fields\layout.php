<?php
/**
 *
 * Layout theme options
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */

// Layout fields.
$dmrthema_default_options = dmrthema_get_option_defaults();

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_general_heading_1',
		'section'  => 'dmrthema_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Wrapper', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_wrapper',
		'label'    => esc_attr__( 'Contain the grid?', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_general',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'dmrthema' ),
			'no'  => esc_attr__( 'No', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

// Wrapper width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_wrapper_width_nb',
		'label'       => esc_html__( 'Wraper container width', 'dmrthema' ),
		'description' => esc_html__( 'Adjust wrapper width in px.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_general',
		'default'     => 2170,
		'priority'    => 10,
		'choices'     => array(
			'min'  => 992,
			'max'  => 3000,
			'step' => 1,
		),
		'active_callback'    => array(
			array(
				'setting'  => 'dmrthema_layout_wrapper',
				'value'    => 'yes',
				'operator' => '==',
			),
		),
		'output'      => array(
			array(
				'element'  => '#page',
				'property' => 'max-width',
				'units'    => 'px',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_general_heading_2',
		'section'  => 'dmrthema_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Content container', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Content Container width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_container_width',
		'label'       => esc_html__( 'Content container width', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the width of your content container in pixels. Default is 1170px.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_general',
		'default'     => 1170,
		'priority'    => 10,
		'choices'     => array(
			'min'  => 992,
			'max'  => 2000,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.col-full, .single-product .site-content .dmrthema-sticky-add-to-cart .col-full, body .woocommerce-message, .single-product .site-content .commercekit-sticky-add-to-cart .col-full, .wc-block-components-notice-banner',
				'property' => 'max-width',
				'units'    => 'px',
			),
			array(
				'element'  => 'body.header-4:not(.full-width-header) .header-4-inner,
				.summary form.cart.commercekit_sticky-atc .commercekit-pdp-sticky-inner,
				.commercekit-atc-sticky-tabs ul.commercekit-atc-tab-links,
				.h-ckit-filters.no-woocommerce-sidebar .commercekit-product-filters',
				'property' => 'max-width',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'       => '
			.product-details-wrapper,
			.single-product .woocommerce:has(.woocommerce-message),
			.single-product .woocommerce-Tabs-panel,
			.single-product .archive-header .woocommerce-breadcrumb,
			 .plp-below.archive.woocommerce .archive-header .woocommerce-breadcrumb,
			.related.products,
			.site-content #sspotReviews:not([data-shortcode="1"]),
			.upsells.products,
			.composite_summary,
			.composite_wrap,
			.wc-prl-recommendations,
			.yith-wfbt-section.woocommerce',
				'value_pattern' => 'calc($px + 5.2325em)',
				'property'      => 'max-width',
				'units'         => '',
			),
			array(
				'element'       => '.main-navigation ul li.menu-item-has-children.full-width .container,
				.single-product .woocommerce-error',
				'property'      => 'max-width',
				'units'         => 'px',
			),
			array(
				'element'       => '.below-content .col-full, footer .col-full',
				'value_pattern' => 'calc($px + 40px)',
				'property'      => 'max-width',
				'units'         => '',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_general_heading_3',
		'section'  => 'dmrthema_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Breadcrumbs', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Display Breadcrumbs.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_breadcrumbs',
		'label'     => esc_html__( 'Display breadcrumbs', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_general',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Breadcrumbs type.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_woocommerce_breadcrumbs_type',
		'label'    => esc_attr__( 'Breadcrumbs type', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_general',
		'default'  => $dmrthema_default_options['dmrthema_layout_woocommerce_breadcrumbs_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_display_breadcrumbs',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'default' 		=> esc_attr__( 'Default', 'dmrthema' ),
			'aioseo'  		=> esc_attr__( 'AIOSEO', 'dmrthema' ),
			'rankmath'  	=> esc_attr__( 'Rank Math', 'dmrthema' ),
			'seoframework'  => esc_attr__( 'SEO Framework', 'dmrthema' ),
			'seopress'  	=> esc_attr__( 'SEOPress', 'dmrthema' ),
			'yoast'  		=> esc_attr__( 'Yoast', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_general_heading_4',
		'section'  => 'dmrthema_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Mobile product grid', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_woocommerce_mobile_grid',
		'label'    => esc_attr__( 'Products per row on mobile', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_general',
		'default'  => 'mobile-grid-two',
		'choices'  => array(
			'mobile-grid-one' => esc_attr__( 'One per row', 'dmrthema' ),
			'mobile-grid-two'  => esc_attr__( 'Two per row', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_general_heading_5',
		'section'  => 'dmrthema_layout_section_general',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Widgets', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Disable block editor for widgets.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_widgets_disable_block_editor',
		'label'     => esc_html__( 'Disable block editor for widgets', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_general',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_sidebar_heading_1',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'General', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Enable sidebar cart drawer.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_enable_sidebar_cart',
		'label'     => esc_html__( 'Enable sidebar cart drawer', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Enable single product ajax add to cart.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_single_product_ajax',
		'label'     => esc_html__( 'Enable single product ajax', 'dmrthema' ),
		'description' => esc_html__( 'Add directly to the cart on single products', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_sidebar_heading_2',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Shop', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Display Shop title.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_shop_title',
		'label'     => esc_html__( 'Display shop heading', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Display Products Results Count.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_count',
		'label'     => esc_html__( 'Display product results count', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display Products Sorting.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_sorting',
		'label'     => esc_html__( 'Display product sorting', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display sale flash over image.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'toggle',
		'settings' => 'dmrthema_layout_woocommerce_display_badge',
		'label'    => esc_html__( 'Display sale % discount', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => 1,
		'priority' => 10,
	)
);

// Sale badge type.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_woocommerce_display_badge_type',
		'label'    => esc_attr__( 'Sale badge design', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => $dmrthema_default_options['dmrthema_layout_woocommerce_display_badge_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_display_badge',
				'value'    => 1,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'default' => esc_attr__( 'Circle', 'dmrthema' ),
			'bubble'  => esc_attr__( 'Bubble', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

// Display rating.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_rating',
		'label'     => esc_html__( 'Display rating', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display rating count.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_catalog_reviews_count',
		'label'     => esc_html__( 'Display rating count', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_display_rating',
				'value'    => 1,
				'operator' => '==',
			),
		),
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display category.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_category',
		'label'     => esc_html__( 'Display category', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display image change on hover.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_flip_image',
		'label'     => esc_html__( 'Image change on hover', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Product card display.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_woocommerce_card_display',
		'label'     => esc_html__( 'Product card display', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_woocommerce_card_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'default'   => esc_html__( 'Default', 'dmrthema' ),
			'slide' => esc_html__( 'Slide up', 'dmrthema' ),
		),
	)
);

// CTA button display.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_woocommerce_cta_display',
		'label'     => esc_html__( 'Button display', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_woocommerce_cta_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'hover'   => esc_html__( 'On hover (desktop only)', 'dmrthema' ),
			'static' => esc_html__( 'Static', 'dmrthema' ),
			'no-cta' => esc_html__( 'Remove buttons', 'dmrthema' ),
		),
	)
);

// Text alignment.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_woocommerce_text_alignment',
		'label'     => esc_html__( 'Product text alignment', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_woocommerce_text_alignment'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'product-align-left'   => esc_html__( 'Left', 'dmrthema' ),
			'product-align-center' => esc_html__( 'Center', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_sidebar_heading_3',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Product Categories', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Display subcategories.
/*dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_subcategories',
		'label'     => esc_html__( 'Display subcategories', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);
*/

// Category description layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_woocommerce_category_position',
		'label'     => esc_html__( 'Category description layout', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_woocommerce_category_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'below-header'   => esc_html__( 'Below header', 'dmrthema' ),
			'within-content' => esc_html__( 'Within content', 'dmrthema' ),
		),
	)
);

// Category description display.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_category_description',
		'label'     => esc_html__( 'Display category description', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_category_position',
				'value'    => 'within-content',
				'operator' => '==',
			),
		),
	)
);

// Category image display.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_category_image',
		'label'     => esc_html__( 'Display category image', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_category_position',
				'value'    => 'within-content',
				'operator' => '==',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_sidebar_heading_4',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Single Product', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Enable block editor for PDPs.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_pdp_block_editor',
		'label'     => esc_html__( 'Enable block editor', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Product gallery width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_pdp_gallery_width',
		'label'     => esc_html__( 'Product gallery width', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_pdp_gallery_width'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'skinny'   	=> esc_html__( 'Skinny', 'dmrthema' ),
			'regular' 	=> esc_html__( 'Regular', 'dmrthema' ),
			'wide' 		=> esc_html__( 'Wide (default)', 'dmrthema' ),
		),
	)
);

// PDP short description position.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_pdp_short_description_position',
		'label'     => esc_html__( 'Product short description position', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_pdp_short_description_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'top'   => esc_html__( 'Top', 'dmrthema' ),
			'bottom' 	=> esc_html__( 'Bottom', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_1',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display sticky add to cart bar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_sticky_cart_display',
		'label'     => esc_html__( 'Legacy sticky add to cart bar', 'dmrthema' ),
		'description' => esc_html__( 'We recommend adding a sticky add to cart bar via CommerceKit', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Sticky add to cart bar position.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_woocommerce_sticky_cart_position',
		'label'    => esc_attr__( 'Sticky add to cart bar position', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => $dmrthema_default_options['dmrthema_layout_woocommerce_sticky_cart_position'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_sticky_cart_display',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices'  => array(
			'top' => esc_attr__( 'Top', 'dmrthema' ),
			'bottom'  => esc_attr__( 'Bottom', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_2',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display product meta data.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_meta_display',
		'label'     => esc_html__( 'Display product meta data', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display previous/next products.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_prev_next_display',
		'label'     => esc_html__( 'Display previous/next', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_3',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display PDP cross-sells carousel.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_cross_sells_carousel',
		'label'     => esc_html__( 'Display cross-sells carousel', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => '0',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// PDP cross-sells carousel heading.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_cross_sells_carousel_heading',
		'label'     => esc_html__( 'Cross-sells carousel heading:', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_cross_sells_carousel_heading'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_cross_sells_carousel',
				'value'    => true,
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.cross-sells-heading',
				'function' => 'html',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_4',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Product description container width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_pdp_description_width',
		'label'     => esc_html__( 'Product description container width', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_pdp_description_width'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'full-width'   => esc_html__( 'Full width', 'dmrthema' ),
			'contained' => esc_html__( 'Contained', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_5',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display floating button.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'toggle',
		'settings' => 'dmrthema_layout_floating_button_display',
		'label'    => esc_attr__( 'Display floating button', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'   => '1',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Floating button text.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_layout_floating_button_text',
		'label'     => esc_html__( 'Floating button title:', 'dmrthema' ),
		'description' => esc_html__( 'Content is added within the widget: "Floating Button Modal Content"', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_layout_floating_button_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_floating_button_display',
				'value'    => true,
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.call-back-feature',
				'function' => 'html',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_6',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display related.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_related_display',
		'label'     => esc_html__( 'Display related', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Number of related items.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'number',
		'settings' => 'dmrthema_layout_related_amount',
		'label'    => esc_attr__( 'Number of related items to show', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'   => '4',
		'choices' => array(
		'min' => 0,
		'max' => 6,
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_7',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Display upsells before related.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_upsells_first',
		'label'     => esc_html__( 'Display upsells before related', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Upsells title.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_upsells_title_text',
		'label'     => esc_html__( 'Upsells title', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_upsells_title_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.upsells-title',
				'function' => 'html',
			),
		),
	)
);

// Number of upsells.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'number',
		'settings' => 'dmrthema_layout_upsells_amount',
		'label'    => esc_attr__( 'Number of upsells to show', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'   => '4',
		'choices' => array(
		'min' => 1,
		'max' => 6,
		),
	)
);


dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_sidebar_heading_5',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase; letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Cart and Checkout', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Display progress bar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'toggle',
		'settings' => 'dmrthema_layout_progress_bar_display',
		'label'    => esc_attr__( 'Display progress bar', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => 1,
		'priority' => 10,
	)
);

// Display cross-sells.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_display_cross_sells',
		'label'     => esc_html__( 'Display cross-sells', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Number of cross sells.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'number',
		'settings' => 'dmrthema_layout_cross_sells_amount',
		'label'    => esc_attr__( 'Number of cross-sells to show', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'   => '4',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_display_cross_sells',
				'value'    => true,
				'operator' => '==',
			),
		),
		'choices' => array(
		'min' => 1,
		'max' => 6,
		),
	)
);

// Mobile Cart page layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        	=> 'toggle',
		'settings'    	=> 'dmrthema_layout_woocommerce_mobile_cart_page',
		'label'       	=> esc_attr__( 'Mobile-optimized cart page', 'dmrthema' ),
		'description' 	=> esc_attr__( 'Collapses the cart table on mobile', 'dmrthema' ),
		'section'     	=> 'dmrthema_layout_section_woocommerce',
		'default'   	=> $dmrthema_default_options['dmrthema_layout_woocommerce_mobile_cart_page'],
		'priority'    	=> 10,
	)
);

// Ajax update cart page quantity.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'toggle',
		'settings' => 'dmrthema_ajaxcart_quantity',
		'label'    => esc_attr__( 'Ajax update cart page quantity', 'dmrthema' ),
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => 0,
		'priority' => 10,
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_woocommerce_hr_rule_8',
		'section'  => 'dmrthema_layout_section_woocommerce',
		'default'  => '<div class="kirki-separator" style="margin-top: 10px; margin-bottom: 10px; border-bottom: 1px solid #ddd;"></div>',
		'priority' => 10,
	)
);

// Distration free checkout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'toggle',
		'settings'    => 'dmrthema_layout_woocommerce_simple_checkout',
		'label'       => esc_attr__( 'Distraction-free checkout', 'dmrthema' ),
		'description' => esc_attr__( 'Simplifies the checkout experience for better conversions.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_woocommerce',
		'default'     => 1,
		'priority'    => 10,
	)
);

// Checkout coupon code position.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_checkout_coupon_position',
		'label'     => esc_html__( 'Checkout coupon code position', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_woocommerce',
		'default'   => $dmrthema_default_options['dmrthema_checkout_coupon_position'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'top'   => esc_html__( 'Top', 'dmrthema' ),
			'bottom' => esc_html__( 'Bottom', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_sidebars_heading_0',
		'section'  => 'dmrthema_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'WooCommerce', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);


// WooCommerce Sidebar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_woocommerce_sidebar',
		'label'     => esc_html__( 'WooCommerce Sidebar', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_sidebars',
		'default'   => $dmrthema_default_options['dmrthema_layout_woocommerce_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-woocommerce-sidebar'  => esc_html__( 'Left', 'dmrthema' ),
			'right-woocommerce-sidebar' => esc_html__( 'Right', 'dmrthema' ),
			'no-woocommerce-sidebar'    => esc_html__( 'None', 'dmrthema' ),
		),
	)
);

// WooCommerce Product Category Widget Toggle.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_wc_product_category_widget_toggle',
		'label'     => esc_html__( 'Product Category Widget Toggle', 'dmrthema' ),
		'description' => esc_html__( 'Include expand/collapse buttons to the core WooCommerce product category widget.', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_sidebars',
		'default'   => $dmrthema_default_options['dmrthema_wc_product_category_widget_toggle'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_sidebars_heading_1',
		'section'  => 'dmrthema_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Pages', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Pages Sidebar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_page_sidebar',
		'label'     => esc_html__( 'Page Sidebar', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_sidebars',
		'default'   => $dmrthema_default_options['dmrthema_layout_page_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-page-sidebar'  => esc_html__( 'Left', 'dmrthema' ),
			'right-page-sidebar' => esc_html__( 'Right', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_sidebars_heading_2',
		'section'  => 'dmrthema_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Blog Archives', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Blog Archives Sidebar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_archives_sidebar',
		'label'     => esc_html__( 'Blog Archives Sidebar', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_sidebars',
		'default'   => $dmrthema_default_options['dmrthema_layout_archives_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-archives-sidebar'  => esc_html__( 'Left', 'dmrthema' ),
			'right-archives-sidebar' => esc_html__( 'Right', 'dmrthema' ),
			'no-archives-sidebar'    => esc_html__( 'None', 'dmrthema' ),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_sidebars_heading_3',
		'section'  => 'dmrthema_layout_section_sidebars',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Single Post', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Posts Sidebar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_post_sidebar',
		'label'     => esc_html__( 'Post Sidebar', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_sidebars',
		'default'   => $dmrthema_default_options['dmrthema_layout_post_sidebar'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'left-post-sidebar'  => esc_html__( 'Left', 'dmrthema' ),
			'right-post-sidebar' => esc_html__( 'Right', 'dmrthema' ),
			'no-post-sidebar'    => esc_html__( 'None', 'dmrthema' ),
		),
	)
);

// Sidebar Width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_layout_sidebar_width',
		'label'       => esc_html__( 'Sidebar Width (%).', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the width of the sidebar.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_sidebars',
		'default'     => 17,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  		=> '#secondary',
				'property' 		=> 'width',
				'units'    		=> '%',
				'media_query' 	=> '@media (min-width: 993px)',
			),
		),
	)
);

// Content Width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_layout_content_width',
		'label'       => esc_html__( 'Content Width (%).', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the width of the content.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_sidebars',
		'default'     => 76,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  		=> '.content-area',
				'property' 		=> 'width',
				'units'    		=> '%',
				'media_query' 	=> '@media (min-width: 993px)',
			),
		),
	)
);


// Blog Layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_blog',
		'label'     => esc_html__( 'Blog Layout', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => $dmrthema_default_options['dmrthema_layout_blog'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'list'        => esc_html__( 'List', 'dmrthema' ),
			'flow'        => esc_html__( 'Flow', 'dmrthema' ),
			'grid grid-2' => esc_html__( 'Grid of 2', 'dmrthema' ),
			'grid grid-3' => esc_html__( 'Grid of 3', 'dmrthema' ),
		),
	)
);

// Display blog page title.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_blog_title',
		'label'     => esc_html__( 'Display blog page title', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog summary.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_blog_summary_display',
		'label'     => esc_html__( 'Display blog post summary', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_layout_blog_heading_0',
		'section'  => 'dmrthema_layout_section_blog',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Single Posts', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Display blog author.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_blog_author',
		'label'     => esc_html__( 'Display blog author', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog meta.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_blog_meta',
		'label'     => esc_html__( 'Display blog meta', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display blog previous and next.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_blog_prev_next',
		'label'     => esc_html__( 'Display blog previous/next', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Display single post featured image.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_post_featured_image',
		'label'     => esc_html__( 'Display featured image', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_blog',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Single Post Layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_layout_singlepost',
		'label'       => esc_html__( 'Single Post Layout.', 'dmrthema' ),
		'description' => esc_html__( 'Layout Two is full width and better with the Block Editor.', 'dmrthema' ),
		'section'     => 'dmrthema_layout_section_blog',
		'default'     => $dmrthema_default_options['dmrthema_layout_singlepost'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'singlepost-layout-one' => esc_html__( 'Layout One', 'dmrthema' ),
			'singlepost-layout-two' => esc_html__( 'Layout Two', 'dmrthema' ),
		),
	)
);


// Footer fields.
// Display Below Content Area.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_below_content_display',
		'label'     => esc_html__( 'Show Below Content?', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_footer',
		'default'   => $dmrthema_default_options['dmrthema_below_content_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'dmrthema' ),
			'hide' => esc_html__( 'Hide', 'dmrthema' ),
		),
	)
);

// Display Footer.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_footer_display',
		'label'     => esc_html__( 'Show Footer?', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_footer',
		'default'   => $dmrthema_default_options['dmrthema_footer_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'dmrthema' ),
			'hide' => esc_html__( 'Hide', 'dmrthema' ),
		),
	)
);

// Display Copyright.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_copyright_display',
		'label'     => esc_html__( 'Show Copyright?', 'dmrthema' ),
		'section'   => 'dmrthema_layout_section_footer',
		'default'   => $dmrthema_default_options['dmrthema_copyright_display'],
		'priority'  => 10,
		'transport' => 'refresh',
		'choices'   => array(
			'show' => esc_html__( 'Show', 'dmrthema' ),
			'hide' => esc_html__( 'Hide', 'dmrthema' ),
		),
	)
);



