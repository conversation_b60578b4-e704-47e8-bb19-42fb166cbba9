# Demir Native Customizer Framework

Bu dosya, Demir temasında Kirki eklentisinin yerli bir customizer framework ile değiştirilmesi hakkında bilgi içerir.

## Ya<PERSON><PERSON>lan Değişiklikler

### 1. <PERSON><PERSON>ılığı Kaldırıldı
- `functions.php` dosyasında Kirki artık required plugin olarak tanımlanmıyor
- Tema artık Kirki olmadan tam işlevsellik sağlıyor

### 2. Yerli Customizer Framework Geliştirildi
- `inc/kirki-fallback.php` dosyası tamamen yeniden yazıldı
- Artık sadece fallback değil, tam bir customizer framework
- 212 adet Kirki field'ı destekleniyor

### 3. Yeni Dosyalar Eklendi
- `inc/class-demir-native-css.php` - CSS çıktı sistemi
- `assets/js/customizer-controls.js` - Customizer kontrol JavaScript'i
- `assets/js/customizer-preview.js` - Canlı önizleme JavaScript'i
- `test-native-customizer.php` - Test dosyası

### 4. Desteklenen Özellikler

#### Customizer Kontrolleri
- ✅ Color picker
- ✅ Typography controls (font-family, size, weight, etc.)
- ✅ Slider/Range controls
- ✅ Select/Radio controls
- ✅ Toggle/Checkbox controls
- ✅ Text/Textarea controls
- ✅ Image/Upload controls
- ✅ Radio-image controls
- ✅ Custom HTML separators

#### CSS Çıktı Sistemi
- ✅ Otomatik CSS üretimi
- ✅ Media query desteği
- ✅ Typography CSS çıktısı
- ✅ Color CSS çıktısı
- ✅ Spacing CSS çıktısı
- ✅ Background CSS çıktısı
- ✅ Prefix/Suffix/Units desteği

#### Font Yönetimi
- ✅ Google Fonts yükleme
- ✅ System font desteği
- ✅ Font variant işleme
- ✅ Font optimizasyonu

#### Canlı Önizleme
- ✅ PostMessage transport
- ✅ Renk değişiklikleri
- ✅ Typography değişiklikleri
- ✅ Layout değişiklikleri
- ✅ JavaScript tabanlı önizleme

## Kirki Uyumluluğu

Framework, Kirki ile tam uyumlu çalışır:
- Kirki aktifse, Kirki kullanılır
- Kirki yoksa, yerli framework devreye girer
- API tamamen aynı (`demir_Kirki::add_field`, `demir_Kirki::add_panel`, vb.)

## Test Etme

1. **Test dosyasını çalıştırın:**
   ```
   yoursite.com/wp-content/themes/demir/test-native-customizer.php
   ```

2. **Customizer'ı kontrol edin:**
   - WordPress Admin → Appearance → Customize
   - Tüm paneller ve kontroller görünmeli
   - Değişiklikler canlı önizleme ile çalışmalı

3. **CSS çıktısını kontrol edin:**
   - Sayfa kaynağında `<style id="demir-native-css">` tagını arayın
   - Customizer ayarlarının CSS'e dönüştüğünü kontrol edin

## Performans

### Avantajlar
- ✅ Kirki eklentisi gereksiz
- ✅ Daha az HTTP isteği
- ✅ Daha küçük dosya boyutu
- ✅ Tema ile entegre
- ✅ Özelleştirilebilir

### Optimizasyonlar
- CSS çıktısı cache'leniyor
- Google Fonts optimize ediliyor
- Gereksiz kod yüklenmiyor
- Conditional loading

## Geliştirici Notları

### Yeni Field Ekleme
```php
demir_Kirki::add_field( 'demir_config', array(
    'type'     => 'color',
    'settings' => 'my_custom_color',
    'label'    => 'My Custom Color',
    'section'  => 'my_section',
    'default'  => '#000000',
    'output'   => array(
        array(
            'element'  => '.my-element',
            'property' => 'color',
        ),
    ),
) );
```

### CSS Çıktısı Özelleştirme
```php
// CSS çıktısını filtreleme
add_filter( 'demir_dynamic_css', function( $css ) {
    $css .= '.my-custom-css { color: red; }';
    return $css;
} );
```

### JavaScript Hooks
```javascript
// Customizer preview'da özel işlemler
wp.customize( 'my_setting', function( value ) {
    value.bind( function( newval ) {
        // Canlı önizleme kodu
    } );
} );
```

## Sorun Giderme

### Customizer Boş Görünüyor
1. `test-native-customizer.php` dosyasını çalıştırın
2. JavaScript console'da hata var mı kontrol edin
3. `demir_Kirki` sınıfının yüklendiğini kontrol edin

### CSS Çıktısı Yok
1. `Demir_Native_CSS` sınıfının yüklendiğini kontrol edin
2. Field'ların `output` parametresi var mı kontrol edin
3. Theme mod değerlerinin kaydedildiğini kontrol edin

### Canlı Önizleme Çalışmıyor
1. `customizer-preview.js` dosyasının yüklendiğini kontrol edin
2. JavaScript console'da hata var mı kontrol edin
3. `transport => 'postMessage'` parametresi doğru mu kontrol edin

## Güncelleme Notları

Bu framework, Kirki'nin tüm temel özelliklerini destekler ancak bazı gelişmiş özellikler eksik olabilir:
- Conditional fields (active_callback) basit seviyede
- Repeater fields desteklenmiyor
- Gelişmiş field türleri eksik olabilir

Gerektiğinde bu özellikler eklenebilir.

## Lisans

Bu framework, Demir temasının bir parçasıdır ve aynı lisans altında dağıtılır.
