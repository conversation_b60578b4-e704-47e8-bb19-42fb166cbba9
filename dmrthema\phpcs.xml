<?xml version="1.0"?>
<ruleset name="WordPress Theme">
	<description>PHPCS Ruleset for a WordPress Theme</description>
	<!-- Check files in this directory and subdirectories. -->
	<file>.</file>
	<!-- Prevent sniffs of some directories. -->
	<exclude-pattern>/vendor/</exclude-pattern>
	<exclude-pattern>/node_modules/*</exclude-pattern>
	<!-- Pass flags to PHPCS:
		 p: Show progress of the run.
		 s: Show sniff codes in all reports.
		 v: Print verbose output.
	-->
	<arg value="psv"/>
	<!-- Only check PHP files. -->
	<arg name="extensions" value="php"/>
	<!-- Use the WordPress ruleset, with exclusions. -->
	<rule ref="WordPress">
		<exclude name="Squiz.PHP.CommentedOutCode.Found"/>
	</rule>
	<rule ref="WordPress-Extra" />
	<!-- Page templates currently use underscores for historic reasons. -->
	<rule ref="WordPress.Files.FileName.NotHyphenatedLowercase">
		<exclude-pattern>page_landing.php</exclude-pattern>
	</rule>
	<!-- Verify that no WP functions are used which are deprecated or removed. -->
	<rule ref="WordPress.WP.DeprecatedFunctions">
		<properties>
			<property name="minimum_supported_version" value="4.4"/>
		</properties>
	</rule>
	<!-- Check all globals have the expected prefix. -->
	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array" value="demir"/>
		</properties>
	</rule>
	<!-- Allow theme-specific exceptions to WordPress filename rules. -->
	<rule ref="WordPress.Files.FileName">
		<properties>
			<property name="is_theme" value="true"/>
		</properties>
	</rule>
</ruleset>

