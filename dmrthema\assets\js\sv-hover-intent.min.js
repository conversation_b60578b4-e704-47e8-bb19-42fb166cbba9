(SV=window.SV||{}).HoverIntent=function(e,t){let n={exitDelay:400,interval:100,sensitivity:7},i={},o,s,r,c,l,v,a,u=function(e,t){for(let n in t)e[n]=t[n];return e},f=function(e){o=e.pageX,s=e.pageY},m=function(e){let t=r-o,n=c-s;if(Math.sqrt(t*t+n*n)<i.sensitivity){for(let u of(clearTimeout(a),l))u.isActive&&(i.onExit(u),u.isActive=!1);i.onEnter(e),e.isActive=!0}else r=o,c=s,v=setTimeout(function(){m(e)},i.interval)};!function(e,t){if(!t||!t.onEnter||!t.onExit)throw"onEnter and onExit callbacks must be provided";for(let o of(i=u(n,t),l=e))o.isActive=!1,o.addEventListener("mousemove",f),o.addEventListener("mouseenter",function(e){if(r=e.pageX,c=e.pageY,o.isActive){clearTimeout(a);return}v=setTimeout(function(){m(o)},i.interval)}),o.addEventListener("mouseleave",function(e){clearTimeout(v),o.isActive&&(a=setTimeout(function(){i.onExit(o),o.isActive=!1},i.exitDelay))})}(e,t)};var menuItems=document.querySelectorAll(".menu-primary-menu-container > .menu > .menu-item-has-children"),hcontent=document.querySelector(".site"),hi=new SV.HoverIntent(menuItems,{onEnter:function(e){e.classList.add("visible"),hcontent&&hcontent.classList.add("visible")},onExit:function(e){e.classList.remove("visible"),hcontent&&hcontent.classList.remove("visible")},exitDelay:0,interval:100,sensitivity:7});
