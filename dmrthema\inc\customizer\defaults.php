<?php
/**
 *
 * <PERSON><PERSON> defaults
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */

if ( ! function_exists( 'dmrthema_get_option_defaults' ) ) {

	/**
	 *
	 * Sensible defaults ftw.
	 */
	function dmrthema_get_option_defaults() {
		$defaults = array(

			// Top Bar.
			'dmrthema_layout_top_bar_display'           						=> 'enable',
			'dmrthema_layout_top_bar_mobile'            						=> 'hide',

			'dmrthema_layout_top_bar_background'        						=> '#fff',
			'dmrthema_layout_top_bar_text'              						=> '#323232',
			'dmrthema_layout_top_bar_border'            						=> '#eee',

			// Layout.
			'dmrthema_layout_wrapper'										=> 'no',

			// Sidebars.
			'dmrthema_layout_woocommerce_sidebar'       						=> 'left-woocommerce-sidebar',
			'dmrthema_wc_product_category_widget_toggle'						=> 'disable',
			'dmrthema_layout_archives_sidebar'          						=> 'right-archives-sidebar',
			'dmrthema_layout_post_sidebar'              						=> 'right-post-sidebar',
			'dmrthema_layout_page_sidebar'              						=> 'right-page-sidebar',

			// Header.
			'dmrthema_header_layout'											=> 'default',
			'dmrthema_header_layout_container'								=> 'contained',
			'dmrthema_header_bg_color'                  						=> '#fff',
			'dmrthema_header_border_color'              						=> '#eee',
			'dmrthema_layout_search_display'            						=> 'enable',
			'dmrthema_layout_search_display_type'							=> 'default',
			'dmrthema_mobile_hamburger'                 						=> '#111',
			'dmrthema_mobile_cart_color'										=> '#dc9814',
			'dmrthema_mobile_bg'												=> '#fff',
			'dmrthema_mobile_divider_line'              						=> '#eee',
			'dmrthema_mobile_color'                     						=> '#222',
			'dmrthema_sticky_mobile_header'			   						=> 'enable',
			'dmrthema_search_mobile'											=> 'enable',
			'dmrthema_search_mobile_position'								=> 'within-navigation',
			'dmrthema_mobile_myaccount'										=> 'disable',
			'dmrthema_tagline_display'				  						=> false,

			'dmrthema_menu_display_description'         						=> true,

			'dmrthema_layout_woocommerce_cart_icon'     						=> 'basket',

			'dmrthema_layout_myaccount_display'								=> 'disable',

			'dmrthema_layout_search_title'              						=> 'Search',

			'dmrthema_cart_title'											=> 'Your Cart',
			'dmrthema_cart_below_text'										=> '',
			'dmrthema_sidebar_hide_cart_link'								=> false,
			'dmrthema_minicart_quantity'										=> false,

			// Navigation.
			'dmrthema_navigation_bg_color'              						=> '#222',
			'dmrthema_navigation_border_color'		   						=> '',
			'dmrthema_secondary_navigation_color'       						=> '#404040',
			'dmrthema_navigation_color'                 						=> '#fff',
			'dmrthema_navigation_color_header_4'        						=> '#323232',
			'dmrthema_navigation_color_hover'           						=> '#dc9814',
			'dmrthema_menu_hover_intent'										=> false,

			// Navigation Dropdowns.
			'dmrthema_navigation_dropdown_background'   						=> '#fff',
			'dmrthema_navigation_dropdown_color'        						=> '#323232',
			'dmrthema_navigation_dropdown_hover_color'  						=> '#dc9814',

			// Navigation Cart.
			'dmrthema_cart_color'                       						=> '#fff',
			'dmrthema_cart_hover_color'                 						=> '#fff',
			'dmrthema_cart_icon_color'                  						=> '#dc9814',
			'dmrthema_cart_bubble_background_color'							=> '#444444',
			'dmrthema_cart_bubble_border_color'								=> '#444444',

			// Sticky Header.
			'dmrthema_sticky_header'                    						=> 'enable',
			'dmrthema_logo_mark_image'                  						=> '',

			// Below Header.
			'dmrthema_below_header_bg'                  						=> '#dc9814',
			'dmrthema_below_header_text'                						=> '#fff',

			// General
			'dmrthema_layout_woocommerce_breadcrumbs_type' 					=> 'default',

			// Mobile products per row
			'dmrthema_layout_woocommerce_mobile_grid'						=> 'mobile-grid-two',

			// WooCommerce.
			'dmrthema_layout_woocommerce_text_alignment' 					=> 'product-align-left',
			'dmrthema_layout_woocommerce_cta_display'   						=> 'hover',

			'dmrthema_layout_woocommerce_display_cart'  						=> true,

			'dmrthema_layout_woocommerce_single_product_ajax' 				=> false,

			'dmrthema_layout_woocommerce_display_breadcrumbs' 				=> true,
			'dmrthema_layout_shop_title'										=> false,
			'dmrthema_layout_woocommerce_display_count' 						=> true,
			'dmrthema_layout_woocommerce_display_sorting' 					=> true,
			'dmrthema_layout_woocommerce_display_badge' 						=> true,
			'dmrthema_layout_woocommerce_display_badge_type'					=> 'bubble',
			'dmrthema_layout_woocommerce_display_rating' 					=> true,
			'dmrthema_layout_catalog_reviews_count'							=> false,
			'dmrthema_layout_woocommerce_display_category' 					=> true,
			'dmrthema_layout_woocommerce_prev_next_display' 					=> true,
			'dmrthema_layout_woocommerce_sticky_cart_display' 				=> false,
			'dmrthema_layout_woocommerce_related_display' 					=> true,
			'dmrthema_layout_woocommerce_meta_display'  						=> true,

			'dmrthema_layout_woocommerce_card_display'  						=> 'default',
			'dmrthema_layout_woocommerce_flip_image'    						=> false,

			'dmrthema_layout_woocommerce_enable_sidebar_cart' 				=> true,

			'dmrthema_cross_sells_carousel'									=> false,
			'dmrthema_cross_sells_carousel_heading'      					=> 'Pairs well with',

			'dmrthema_layout_floating_button_display'   						=> true,
			'dmrthema_layout_floating_button_text'      						=> 'Questions? Request a Call Back',

			'dmrthema_layout_related_amount'            						=> 4,
			'dmrthema_layout_upsells_amount'            						=> 4,

			'dmrthema_layout_woocommerce_upsells_first' 						=> false,
			'dmrthema_upsells_title_text'               						=> 'Customers also bought',

			'dmrthema_display_cross_sells'									=> true,
			'dmrthema_layout_cross_sells_amount'        						=> 4,

			'dmrthema_layout_pdp_gallery_width'								=> 'wide',

			'dmrthema_layout_pdp_short_description_position'					=> 'top',
			'dmrthema_layout_pdp_block_editor'								=> true,
			'dmrthema_layout_pdp_description_width'							=> 'full-width',
			'dmrthema_widgets_disable_block_editor'							=> true,

			'dmrthema_layout_progress_bar_display'      						=> true,
			'dmrthema_layout_woocommerce_simple_checkout' 					=> true,

			'dmrthema_layout_woocommerce_mobile_cart_page'					=> true,
			'dmrthema_ajaxcart_quantity'										=> false,

			'dmrthema_checkout_coupon_position'								=> 'bottom',

			'dmrthema_layout_woocommerce_sticky_cart_position' 				=> 'bottom',

			'dmrthema_layout_woocommerce_category_position' 					=> 'within-content',
			'dmrthema_layout_woocommerce_category_image' 					=> true,
			'dmrthema_layout_woocommerce_category_description' 				=> true,

			'dmrthema_mobile_menu_text_display'         						=> 'yes',
			'dmrthema_mobile_menu_text'                 						=> 'MENU',

			// Blog.
			'dmrthema_layout_blog'                      						=> 'grid grid-2',
			'dmrthema_layout_blog_title'                      				=> true,
			'dmrthema_layout_blog_summary_display'      						=> true,
			'dmrthema_layout_singlepost'                						=> 'singlepost-layout-one',
			'dmrthema_layout_blog_author'               						=> true,
			'dmrthema_layout_blog_meta'                 						=> true,
			'dmrthema_layout_blog_prev_next'            						=> false,
			'dmrthema_post_featured_image'              						=> true,

			// Colors.
			'dmrthema_color_general_swatch'             						=> '#dc9814',

			'dmrthema_color_general_links'              						=> '#1e68c4',
			'dmrthema_color_general_links_hover'        						=> '#111',

			'dmrthema_color_body_bg'                    						=> '#fff',

			'dmrthema_product_bg'                       						=> '#f8f8f8',

			'dmrthema_ratings_color'                    						=> '#ee9e13',

			'dmrthema_woocommerce_button_text'          						=> '#fff',
			'dmrthema_woocommerce_button_bg'            						=> '#3bb54a',
			'dmrthema_woocommerce_button_hover_bg'      						=> '#009245',

			'dmrthema_sale_flash_bg'                    						=> '#3bb54a',
			'dmrthema_sale_flash_text'                  						=> '#fff',

			'dmrthema_floating_button_bg'               						=> '#dc9814',
			'dmrthema_floating_button_text'             						=> '#fff',

			'dmrthema_archives_description_bg'          						=> '#efeee3',
			'dmrthema_archives_description_text'        						=> '#222',

			'dmrthema_progress_bar_color'               						=> '#3bb54a',

			// Footer.
			'dmrthema_below_content_display'            						=> 'show',
			'dmrthema_footer_display'                   						=> 'show',
			'dmrthema_copyright_display'                						=> 'show',

			'dmrthema_below_content_icons'              						=> '#999',

			'dmrthema_footer_bg'                       						=> '#111',
			'dmrthema_footer_heading_color'             						=> '#fff',
			'dmrthema_footer_color'                     						=> '#ccc',
			'dmrthema_footer_links_color'               						=> '#999',
			'dmrthema_footer_links_hover_color'         						=> '#fff',

			// Speed Settings.
			'dmrthema_general_speed_critical_css'       						=> 'no',
			'dmrthema_general_speed_minify_main_css'    						=> 'yes',
			'dmrthema_general_speed_rivolicons'         						=> 'no',

		);

		return apply_filters( 'dmrthema_get_option_defaults', $defaults );
	}
}// End if().


