<?php
/**
 *
 * Kirki menu section
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */
function dmrthema_kirki_section_mainmenu( $wp_customize ) {

	// Top Bar.
	$wp_customize->add_section( 'dmrthema_header_section_top_bar', array(
		'title'			 => esc_html__( 'Top Bar', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Header Layout.
	$wp_customize->add_section( 'dmrthema_header_section_layout', array(
		'title'			 => esc_html__( 'Header', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Mobile Header
	$wp_customize->add_section(
	'dmrthema_section_general_mobile_header', array(
		'title'    => esc_html__( 'Mobile Header', 'dmrthema' ),
		'panel'    => 'dmrthema_panel_mainmenu',
		'priority' => 10,
	)
	);

	// Navigation.
	$wp_customize->add_section( 'dmrthema_navigation_section_layout', array(
		'title'			 => esc_html__( 'Navigation', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Cart.
	$wp_customize->add_section( 'dmrthema_cart_section_layout', array(
		'title'			 => esc_html__( 'Cart', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Below Header.
	$wp_customize->add_section( 'dmrthema_below_header_section_layout', array(
		'title'			 => esc_html__( 'Below Header', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );

	// Responsive Breakpoint.
	$wp_customize->add_section( 'dmrthema_mainmenu_section_responsive_breakpoint', array(
		'title'			 => esc_html__( 'Responsive Breakpoint', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_mainmenu',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'dmrthema_kirki_section_mainmenu' );


