<?php
/**
 *
 * General theme options
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */

// General fields.
$dmrthema_default_options = dmrthema_get_option_defaults();

// Header Logo Height.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_logo_height',
		'label'       => esc_html__( 'Logo height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the height of your logo in pixels. You can upload your logo image within the "Site Identity" panel.', 'dmrthema' ),
		'section'     => 'dmrthema_section_general_logo',
		'default'     => 38,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'output'      => array(
			array(
				'element'  => '.site-header .custom-logo-link img',
				'property' => 'height',
				'units'    => 'px',
			),
		),
	)
);

// Header 4 (One row) Logo Height.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_logo_height_header4',
		'label'       => esc_html__( 'Logo height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the height of your logo in pixels. You can upload your logo image within the "Site Identity" panel.', 'dmrthema' ),
		'section'     => 'dmrthema_section_general_logo',
		'default'     => 30,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'output'      => array(
			array(
				'element'  => '.header-4 .site-header .custom-logo-link img',
				'property' => 'height',
				'units'    => 'px',
			),
		),
	)
);

// Display tagline under logo.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'toggle',
		'settings' => 'dmrthema_tagline_display',
		'label'    => esc_attr__( 'Display tagline under logo', 'dmrthema' ),
		'description'    => esc_attr__( 'This is set within Settings > General', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_logo',
		'default'  => $dmrthema_default_options['dmrthema_tagline_display'],
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Sticky Logo Image.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'image',
		'settings' => 'dmrthema_logo_mark_image',
		'label'    => esc_html__( 'Sticky logo', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_sticky_logo',
		'default'  => $dmrthema_default_options['dmrthema_logo_mark_image'],
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
	)
);


// Sticky Logo Image Width.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'slider',
		'settings' => 'dmrthema_sticky_logo_width',
		'label'    => esc_html__( 'Sticky logo width', 'dmrthema' ),
		'description'    => esc_attr__( 'Suggested width of at least 60', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_sticky_logo',
		'default'  => 60,
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'  => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'output'   => array(
			array(
				'element'  => '.is_stuck .logo-mark',
				'property' => 'width',
				'units'    => 'px',
			),
			array(
				'element'  => '.is_stuck .primary-navigation.with-logo .menu-primary-menu-container',
				'property' => 'margin-left',
				'units'    => 'px',
				'media_query'   => '@media (min-width: 993px)',
			),
		),
	)
);

// Mobile Header Height.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_mobile_header_height',
		'label'       => esc_html__( 'Mobile header height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust height of your mobile header (px)', 'dmrthema' ),
		'section'     => 'dmrthema_section_general_mobile_header',
		'default'     => 70,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.main-header, .site-branding',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'       => '.main-header .site-header-cart',
				'value_pattern' => 'calc(-14px + $px / 2)',
				'property'      => 'top',
				'units'         => '',
				'media_query'   => '@media (max-width: 992px)',
			),
			array(
				'element'       => '.sticky-m .mobile-filter, .sticky-m #cgkitpf-horizontal',
				'property'      => 'top',
				'units'         => 'px',
				'media_query'   => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.sticky-m .commercekit-atc-sticky-tabs',
				'value_pattern' => 'calc($px - 1px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.m-search-bh.sticky-m .commercekit-atc-sticky-tabs, .m-search-toggled.sticky-m .commercekit-atc-sticky-tabs',
				'value_pattern' => 'calc($px + 60px - 1px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.m-search-bh.sticky-m .mobile-filter, .m-search-toggled.sticky-m .mobile-filter, .m-search-bh.sticky-m #cgkitpf-horizontal, .m-search-toggled.sticky-m #cgkitpf-horizontal',
				'value_pattern' => 'calc($px + 60px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.sticky-m .cg-layout-vertical-scroll .cg-thumb-swiper',
				'value_pattern' => 'calc($px + 10px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),

		),
	)
);

// Mobile Logo Height.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_mobile_logo_height',
		'label'       => esc_html__( 'Mobile logo height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust height of your mobile logo (px)', 'dmrthema' ),
		'section'     => 'dmrthema_section_general_mobile_header',
		'default'     => 22,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => 'body.theme-dmrthema .site-header .custom-logo-link img,
				body.wp-custom-logo .site-header .custom-logo-link img',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (max-width: 992px)',
			),
		),
	)
);

// Mobile Sticky Header.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_sticky_mobile_header',
		'label'    => esc_attr__( 'Mobile sticky header', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_mobile_header',
		'default'  => $dmrthema_default_options['dmrthema_sticky_mobile_header'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

// Display Search on Mobile.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_search_mobile',
		'label'     => esc_html__( 'Show search on mobile', 'dmrthema' ),
		'section'   => 'dmrthema_section_general_mobile_header',
		'default'  => $dmrthema_default_options['dmrthema_search_mobile'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

// Mobile Search Position.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_search_mobile_position',
		'label'     => esc_html__( 'Mobile search position', 'dmrthema' ),
		'section'   => 'dmrthema_section_general_mobile_header',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_search_mobile',
				'value'    => 'enable',
				'operator' => '==',
			),
		),
		'choices'  => array(
			'within-navigation' => esc_attr__( 'Within navigation', 'dmrthema' ),
			'below-header'  => esc_attr__( 'Below header bar', 'dmrthema' ),
			'toggle'  => esc_attr__( 'Header icon and toggle', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

// Display My Account on Mobile.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_mobile_myaccount',
		'label'     => esc_html__( 'Show my account on mobile', 'dmrthema' ),
		'section'   => 'dmrthema_section_general_mobile_header',
		'default'  => $dmrthema_default_options['dmrthema_mobile_myaccount'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

// Display Mobile Menu label.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_mobile_menu_text_display',
		'label'    => esc_attr__( 'Display mobile menu label', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_mobile_header',
		'default'  => $dmrthema_default_options['dmrthema_mobile_menu_text_display'],
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'dmrthema' ),
			'no'  => esc_attr__( 'No', 'dmrthema' ),

		),
		'priority' => 10,
	)
);

// Mobile Menu label text.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_mobile_menu_text',
		'label'     => esc_html__( 'Mobile menu label text', 'dmrthema' ),
		'section'   => 'dmrthema_section_general_mobile_header',
		'default'   => $dmrthema_default_options['dmrthema_mobile_menu_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_mobile_menu_text_display',
				'value'    => 'yes',
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.bar-text',
				'function' => 'html',
			),
		),
	)
);

// Critical CSS Settings.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_general_speed_heading_1',
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Critical CSS', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Critical CSS.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_general_speed_critical_css',
		'label'    => esc_attr__( 'Enable critical CSS?', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'dmrthema' ),
			'no'  => esc_attr__( 'No', 'dmrthema' ),

		),
		'priority' => 10,
	)
);


// Minification Settings.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_general_speed_heading_2',
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Minification Settings', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Main CSS Minified.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_general_speed_minify_main_css',
		'label'    => esc_attr__( 'Load minified CSS files?', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => 'yes',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'dmrthema' ),
			'no'  => esc_attr__( 'No', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

// Icon Font.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_general_speed_heading_3',
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Icon Font', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Rivolicons.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_general_speed_rivolicons',
		'label'    => esc_attr__( 'Load Rivolicons icon font?', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_speed_settings',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'dmrthema' ),
			'no'  => esc_attr__( 'No', 'dmrthema' ),
		),
		'priority' => 10,
	)
);



