<?php
/**
 *
 * Kirki layout section
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */
function dmrthema_kirki_section_layout( $wp_customize ) {

	$wp_customize->add_section( 'dmrthema_layout_section_general', array(
		'title'			 => esc_html__( 'General', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_layout_section_sidebars', array(
		'title'			 => esc_html__( 'Sidebars', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_layout_section_blog', array(
		'title'			 => esc_html__( 'Blog', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_layout_section_woocommerce', array(
		'title'			 => esc_html__( 'WooCommerce', 'dmrthema' ),
		'description'	 => esc_html__( 'Publish and refresh to see the changes.', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_layout',
		'priority'		 => 10,
	) );

	$wp_customize->add_section( 'dmrthema_layout_section_footer', array(
		'title'			 => esc_html__( 'Footer', 'dmrthema' ),
		'panel'			 => 'dmrthema_panel_layout',
		'priority'		 => 10,
	) );
}

add_action( 'customize_register', 'dmrthema_kirki_section_layout' );


