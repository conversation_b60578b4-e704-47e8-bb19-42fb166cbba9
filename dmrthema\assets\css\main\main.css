/*
Reset
========
*/
/*! modern-normalize v2.0.0 | MIT License | https://github.com/sindresorhus/modern-normalize */
/* -- Use a better box model (opinionated) -- */
*,
::before,
::after {
  box-sizing: border-box;
}
html {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
     tab-size: 4;
}
/* -- Sections -- */
body {
  margin: 0;
}
/* -- Rule -- */
hr {
  height: 0;
  color: inherit;
}
/* -- Add the correct font weight in Edge and Safari -- */
b,
strong {
  font-weight: bolder;
}
/* -- Fonts -- */
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 1em;
}
small {
  font-size: 80%;
}
/* -- Prevent 'sub' and 'sup' elements from affecting the line height in all browsers -- */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/* -- Tables -- */
table {
  text-indent: 0;
  border-color: inherit;
}
/* -- Forms -- */
button,
select {
  text-transform: none;
}
/* -- Correct the inability to style clickable types in iOS and Safari. -- */
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
/* -- Remove the inner border and padding in Firefox. -- */
::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
/* -- Restore the focus styles unset by the previous rule. -- */
:-moz-focusring {
  outline: 1px dotted ButtonText;
}
:-moz-ui-invalid {
  box-shadow: none;
}
/* -- Remove padding so developers are not caught out when they zero out 'fieldset' elements in all browsers. -- */
legend {
  padding: 0;
}
/* -- Add the correct vertical alignment in Chrome and Firefox. -- */
progress {
  vertical-align: baseline;
}
/* -- Correct the cursor style of increment and decrement buttons in Safari. -- */
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/* -- Correct the odd appearance in Chrome and Safari -- */
[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
/* --Remove the inner padding in Chrome and Safari on macOS. --*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/* -- Correct the inability to style clickable types in iOS and Safari. -- */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
/* -- Interactive -- */
summary {
  display: list-item;
}
.is-layout-flow summary + * {
    margin-top: 0;
}
/*
Reset Extended
========
*/
html {
  -webkit-text-size-adjust: 100%;
     -moz-text-size-adjust: 100%;
          text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  overflow-wrap: break-word;
  scrollbar-gutter: stable;
}
/* -- Elements 'display: block' by default. -- */
img, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}
/* -- Constrain images and videos and preserve aspect ratio. -- */
img, picture, video, canvas {
  display: block;
  max-width: 100%;
  height: auto;
}
/* -- Pretty text wrapping -- */
h1, h2, h3, h4 {
  text-wrap: pretty;
}
/* -- Disable zoom on tappable elements -- */
a, button, input {
  touch-action: manipulation;
}
/* -- Smooth text -- */
body, button, input, textarea {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* -- Removes the default spacing and border -- */
blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
  border: 0;
}
ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/* -- Prevent resizing textareas horizontally -- */
textarea {
  resize: vertical;
}
/* -- Make elements stay hidden by default -- */
[hidden] {
  display: none;
}
/* Set spacing between cells to 0. Remove gaps between borders -- */
table {
  border-spacing: 0;
  border-collapse: collapse;
}
/* -- Buttonsm-- */
button,
[role="button"] {
  cursor: pointer;
}
button::-moz-focus-inner,input::-moz-focus-inner {
  padding: 0;
  border: 0
}
/* -- Prevent pointer cursor. -- */
:disabled {
  cursor: default;
}
/* -- Forms -- */
button,input:where([type='button']),input:where([type='reset']),input:where([type='submit']) {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}
/* -- Animations, scrolling effects, transitions are reduced -- */
@media (prefers-reduced-motion: reduce) {
  *, ::before, ::after {
    animation-delay: -1ms !important;
    animation-duration: 1ms !important;
    animation-iteration-count: 1 !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}
mark {
  background-color: transparent;
}
address {
  font-style: normal;
}
article,aside,figcaption,figure,footer,header,hgroup,main,menu,nav,section {
  display: block;
}
audio,canvas,progress,video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
a {
  color: #2c2d33;
  background-color: transparent;
  text-decoration: none;
  transition: color 0.2s ease;
}
a:active, a:hover {
  outline: 0;
}
a, button, input {
  touch-action: manipulation;
}
cite {
  display: block;
  margin-top: 1rem;
  font-style: normal;
}
mark {
  color: #000;
  background: #ff0;
}
small {
  font-size: 75%;
}
img {
  max-width: 100%;
  height: auto;
  border: 0;
  border-radius: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 0;
}
optgroup {
  margin: 0;
  color: inherit;
}
input[type=checkbox],input[type=radio] {
  box-sizing: border-box;
}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button {
  height: auto;
}
input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  margin: 1.618em 0;
  padding: 1em 1.618em 1.618em;
  border: 0;
}
legend {
  border: 0;
}
table {
  width: 100%;
  border-spacing: 0;
  border-collapse: separate;
  font-size: 15px;
}
legend, ol, td, th, ul {
  padding: 0;
}
address,p,table {
  margin: 0 0 1.21575rem;
}
p:empty {
  margin: 0;
}
ol, ul {
  margin: 0 0 1.41575em 20px;
}
ul {
  list-style: disc;
}
ol {
  list-style: decimal;
}
li > ol, li > ul {
  margin-bottom: 0;
  margin-left: 1em;
}
b,dt,strong {
  font-weight: 600;
}
dd {
  margin: 0 0 1.618em;
}
pre {
  overflow: auto;
  max-width: 100%;
  margin-bottom: 1.618em;
  padding: 1.618em;
  background: rgba(0,0,0,.1);
  font-family: "Courier 10 Pitch",Courier,monospace;
}
code,kbd,tt,var {
  padding: .202em .5407911001em;
  background-color: rgba(0,0,0,.05);
  font-family: Monaco,Consolas,"Andale Mono","DejaVu Sans Mono",monospace;
}
abbr,acronym {
  border-bottom: 1px dotted #666;
  cursor: help;
}
ins,mark {
  background: 0 0;
  font-weight: 400;
  text-decoration: none;
}
table td,
table th {
  text-align: left;
  vertical-align: middle;
}
table caption,
table th {
  font-weight: 600;
}
select, embed, iframe, object, video {
  max-width: 100%;
}
/* -- Gallery -- */
.gallery {
  margin-bottom: 1em;
}
.gallery .gallery-item {
  float: left;
}
.gallery {
  margin-left: -15px;
  margin-right: -15px;
  overflow: hidden;
}
.gallery .gallery-item {
  margin-bottom: 2em;
  padding: 0 15px;
}
.gallery .gallery-item .gallery-icon a {
  display: block;
}
.gallery .gallery-item .gallery-icon img {
  height: auto;
  margin: 0 auto;
  border: 0;
}
.gallery .gallery-item .gallery-caption {
  margin: 10px 0 0 0;
  text-align: center;
}
.gallery.gallery-columns-1 .gallery-item {
  width: 100%;
}
.gallery.gallery-columns-2 .gallery-item {
  width: 50%;
}
.gallery.gallery-columns-2 .gallery-item:nth-child(2n+1) {
  clear: left;
}
.gallery.gallery-columns-3 .gallery-item {
  width: 33.3%;
}
.gallery.gallery-columns-3 .gallery-item:nth-child(3n+1) {
  clear: left;
}
.gallery.gallery-columns-4 .gallery-item {
  width: 25%;
}
.gallery.gallery-columns-4 .gallery-item:nth-child(4n+1) {
  clear: left;
}
.gallery.gallery-columns-5 .gallery-item {
  width: 20%;
}
.gallery.gallery-columns-5 .gallery-item:nth-child(5n+1) {
  clear: left;
}
.gallery.gallery-columns-6 .gallery-item {
  width: 16.666666667%;
}
.gallery.gallery-columns-7 .gallery-item {
  width: 14.285714286%;
}
.gallery.gallery-columns-8 .gallery-item {
  width: 12.5%;
}
.gallery.gallery-columns-9 .gallery-item {
  width: 11.111111111%
}
/*
Forms
========
*/
button, input, select {
  vertical-align: baseline;
}
button {
  border: none;
}
input[type="number"] {
  -webkit-appearance: none;
  border-radius: 0;
}
.form-row input,
.form-row select,
.form-row textarea {
  width: 100%;
}
.form-row label {
  display: block;
  margin-bottom: 5px;
  color: #111;
  font-size: 14px;
  font-weight: 600;
}
.form-row .button,
.form-row input[type="checkbox"],
.form-row input[type="radio"] {
  width: auto;
}
.form-row.woocommerce-validated input.input-text {
  border-color: #0f834d;
}
.form-row.woocommerce-invalid input.input-text {
  border-color: #e2401c;
}
.form-row-last {
  margin-right: 0!important;
}
.form-row-wide {
  clear: both;
  width: 100%;
}
@media (min-width:993px) {
  .col2-set {
    float: left;
    width: 52.9411764706%;
    margin-right: 5.8823529412%;
  }
  .col2-set .form-row-first {
    float: left;
    width: 48%;
    margin-right: 0;
  }
  .col2-set .form-row-last {
    float: right;
    width: 48%;
    margin-right: 0
  }
  .col2-set .col-1,.col2-set .col-2 {
    margin-bottom: 1.618em
  }
  .form-row-first {
    float: left;
    clear: both;
    width: 47.0588235294%;
    margin-right: 5.8823529412%;
  }
  .form-row-last {
    float: right;
    width: 47.0588235294%;
    margin-right: 0;
  }
}
.required {
  border-bottom: 0;
  color: #df0202;
  text-decoration: none;
}
.woocommerce-input-wrapper {
  display: block;
}
label.inline input {
  width: auto;
}
fieldset legend {
  width: calc(100% + 60px);
  margin-left: -30px;
  padding: 10px 30px 10px;
  color: #111;
  font-size: 20px;
  font-weight: 600;
}
/* -- Select -- */
select {
  width: 100%;
  padding: 0 40px 0 0.7em;
  padding-right: 40px;
  background: url("data:image/svg+xml;charset=utf8,%3Csvg width='1792' height='1792' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Ctitle%3Ebackground%3C/title%3E%3Crect fill='none' id='canvas_background' height='402' width='582' y='-1' x='-1'/%3E%3C/g%3E%3Cg%3E%3Ctitle%3ELayer 1%3C/title%3E%3Cpath fill='%23888' id='svg_1' d='m1408,704q0,26 -19,45l-448,448q-19,19 -45,19t-45,-19l-448,-448q-19,-19 -19,-45t19,-45t45,-19l896,0q26,0 45,19t19,45z'/%3E%3C/g%3E%3C/svg%3E") calc(100% - 12px) 12px no-repeat;
  margin: 0;
  background-size: 15px 15px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: 0;
  border: 1px solid #d2d6dc;
  border-radius: 0.3rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  height: 40px;
}
select:focus {
  outline: 0;
}
@media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {
  select {
    padding-right: 0;
    background-image: none;
  }
}
/* -- Buttons -- */
.button, input[type="button"], input[type="reset"], input[type="submit"] {
  display: inline-block;
  padding: 0.8180469716em 1.41575em;
  border: 0;
  border-color: #43454b;
  border-radius: 4px;
  font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
  outline: 0;
  background: 0 0;
  background-color: #43454b;
  font-weight: 600;
  text-shadow: none;
  text-decoration: none;
  cursor: pointer;
  -webkit-appearance: none;
  transition: 0.2s all;
}
input[type="button"], input[type="reset"], input[type="submit"] {
  color: #fff;
  font-weight: 600;
}
/* -- Checkboxes and Radio -- */
input[type="checkbox"],
input[type="radio"] {
  padding: 0;
}
/* -- Inputs -- */
input[type="tel"],
input[type="text"],
input[type="url"] {
  width: 100%;
}
input[type="password"],
input[type="search"],
input[type="email"],
input[type="text"],
input[type="number"],
input[type="tel"] {
  outline: 0;
  margin-bottom: 0;
  border: 1px solid #d2d6dc;
  border-radius: 0.3rem;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  height: 40px;
  padding: 0 0.7em;
}
input[type=text].select2-search__field {
    height: auto;
}
/* -- Search -- */
input[type="search"] {
  padding-left: 43px;
}
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
input[type="search"]::-moz-placeholder {
  color: #555;
}
input[type="search"]::placeholder {
  color: #555;
}
.site-search input[type="search"] {
  border: 0;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
}
.site-search form:before {
  left: 15px;
  top: 50%;
  margin-top: -7px;
  width: 16px;
  height: 16px;
}
/* -- Magnify Icon -- */
.woocommerce-product-search:before,
.widget_search:before,
form.search-form:before,
.wp-block-search__inside-wrapper:before {
  width: 18px;
  height: 18px;
  content: "";
  display: inline-block;
  position: absolute;
  z-index: 1;
  top: 0.7em;
  left: 0.8em;
  background-color: #ccc;
  -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
          mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  -webkit-mask-position: center;
          mask-position: center;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  -webkit-mask-size: contain;
          mask-size: contain;
}
/* -- Textarea -- */
textarea {
  overflow: auto;
  border: 1px solid #d2d6dc;
  border-radius: 0.25rem;
  padding: 0.8em;
  outline: 0;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
  width: 100%;
  padding-left: 0.875em;
  vertical-align: top;
  height: 150px;
}
/* -- Focus -- */
.input-text:focus, input[type=email]:focus, input[type=password]:focus, input[type=search]:focus, input[type=tel]:focus,input[type=text]:focus,input[type=url]:focus, textarea:focus {
  border-color: #999;
}
/*
Styleguide
========
*/
/* Links */
.below-woocommerce-category a, .product-widget a,
.post-meta a, .comment-text a, .comment-respond p a, .product_meta a, .woocommerce-tabs table.woocommerce-product-attributes a,
.entry-content article a:not(.elementor-button), a.reset_variations,
body:not(.elementor-page):not(.woocommerce-account):not(.woocommerce-order-received) .entry-content p:not(.product__categories) a:not(.button) {
  text-decoration: underline;
  text-underline-offset: 0.12em;
  text-decoration-thickness: 0.75px;
}
/* -- Horizontal Rule -- */
hr {
  height: 0;
  margin-top: 38px;
  margin-bottom: 38px;
  border: 0;
  border-top: 1px solid #e9e9e9;
}
/* -- Headings -- */
h1,h2 {
  margin: 0 0 1rem;
}
h3,h4,h5,h6 {
  margin: 0 0 0.5rem;
}
h5 {
  font-size: 1.2em;
}
h6 {
  font-size: 1em;
}
blockquote+h2,blockquote+h3,blockquote+h4,blockquote+header h2,form+h2,form+h3,form+h4,form+header h2,ol+h2,ol+h3,ol+h4,ol+header h2,p+h2,p+h3,p+h4,p+header h2,table+h2,table+h3,table+h4,table+header h2,ul+h2,ul+h3,ul+h4,ul+header h2 {
  margin-top: 1.2906835em;
}
/* -- Definition Lists -- */
dl.variation {
  margin: 0.4em 0;
  font-size: 0.9em;
  color: #545454;
}
dl.variation dd {
  margin-bottom: 0;
  margin-right: 5px;
  color: #323232;
}
dl.variation dt {
  line-height: 1.2;
}
dl.variation dd p {
  margin-bottom: 0;
}
dl.variation dd,
dl.variation dt,
dl.variation dd p {
  display: inline;
}
/* -- Video Container -- */
.video-container {
  position: relative;
  margin-bottom: 30px;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}
.video-container:last-child {
    margin-bottom: 0;
}
.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/* -- Captions -- */
.wp-block-image figcaption {
  margin-top: 0.8em;
  color: #555;
  font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
  text-align: center;
}
.wp-caption {
  max-width: 100%;
  margin-bottom: 1em;
}
.wp-caption img[class*="wp-image-"] {
  display: block;
  margin: 0 auto;
}
.wp-caption .wp-caption-text {
  font-size: 14px;
}
/* Alignments */
.alignleft,
.alignright {
  display: inline;
  margin-top: 5px;
}
.alignleft {
  float: left;
  margin-right: 2.5em;
}
.alignright {
  float: right;
  margin-left: 2.5em;
}
.aligncenter {
  display: block;
  clear: both;
  margin: 0 auto;
}
@media only screen and (min-width: 1070px) {
    .alignfull img,
    .alignwide img {
        display: block;
        margin: 0 auto;
    }
}
/* -- Blockquote -- */
.content-area blockquote {
  position: relative;
  margin: 2.5em 40px;
}
.content-area blockquote.alignleft,
.content-area blockquote.alignright {
  margin-top: 0.6em;
  padding: 2em 0;
}
.content-area blockquote p {
  margin-bottom: 0;
}
.content-area blockquote::before {
  position: absolute;
  top: -12px;
  left: -40px;
  margin: 0;
  color: #ccc;
  font-family: Georgia, serif;
  font-size: 50px;
  content: "\201c";
}
.content-area h3 + blockquote {
  margin-top: 10px;
}
/* -- Embed -- */
.entry-content figure.wp-block-embed {
  width: 100%;
  margin: 0;
}
/* -- Lists -- */
.entry-content ol ol,
.entry-content ul ul {
  margin: 1em 0 1em 2em;
}
.entry-content ol li {
  padding-left: 6px;
}
/* -- Mobile -- */
@media (max-width: 768px) {
  .site,
  .entry-content {
    font-size: 15px;
  }
  input, select, textarea {
    font-size: 16px;
  }
  .entry-content:not(.wc-tab) p.form-row {
    font-size: 14px;
  }
  .site h1 {
    font-size: 30px;
    letter-spacing: -0.01em;
  }
  .site h2 {
    font-size: clamp(1.375rem, 0.9939rem + 1.2195vw, 1.75rem);
    letter-spacing: -0.01em;
  }
}
/*
404
========
*/
body.error404 h1 {
    margin-bottom: 0.5rem;
}
/*
Header Desktop
========
*/
@media (min-width: 993px) {
    /* Hide these on desktop */
    .site-branding + .dmrthema-myaccount,
    .mobile-summary {
        display: none;
    }
    .site-header .col-full {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .site-header {
        position: relative;
        z-index: 20; /* Required for search suggestions to appear */
    }
    body:has(.commercekit-waitlist-popup--active) .site-header {
        z-index: 2;
    }
    .site-header .site-branding {
        line-height: 1;
    }
    .site-branding .site-title {
        margin: 0;
        font-size: 20px;
    }
    .site-branding .logo {
        display: inline-block;
        margin: 0;
    }
    .site-header .custom-logo-link img {
        width: auto;
    }
    .site-header .site-search {
        margin-left: 3em;
        flex-grow: 1;
    }
    .site-search input {
        padding-top: 1.45rem;
        padding-bottom: 1.45rem;
    }
    .site-header .widget {
        margin-bottom: 0;
    }
    .site-branding a {
        color: #111;
        font-weight: bold;
    }
    .site-branding p {
        margin-top: 5px;
        margin-bottom: 0;
        color: #555;
        font-size: 12px;
    }
    .site-branding .site-description {
        display: block;
        margin-top: 0.35rem;
        font-size: 0.755em;
    }
    .main-header {
        display: flex;
    }
    /* -- Search style -- */
    .site-search.type-outline input[type="search"] {
      background-color: #fff;
      border: 1px solid #e2e2e2;
    }
    /* -- Header 2 and 3: Centered -- */
    .header-2 .main-header,
    .header-3 .main-header {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .header-2 .site-header,
    .header-3 .site-header {
        position: relative;
    }
    .header-2 .site-header .site-search {
        order: 1;
    }
    .header-3 .site-header .site-search {
        order: 3;
    }
    .header-2 .site-header .site-search,
    .header-3 .site-header .site-search {
        max-width: 25%;
        margin: 0;
    }
    .header-2 .site-header .site-branding,
    .header-3 .site-header .site-branding {
        position: absolute;
        top: 50%;
        left: 50%;
        order: 2;
        text-align: center;
        transform: translate(-50%, -50%);
    }
    .header-2 .site-header .secondary-navigation,
    .header-3 .site-header .secondary-navigation {
        padding: 0;
    }
    .header-2 .site-header .secondary-navigation {
        order: 3;
    }
    .header-3 .site-header .secondary-navigation {
        order: 1;
        margin-right: auto;
        margin-left: -18px;
    }
    .header-2 .col-full-nav,
    .header-3 .col-full-nav {
        order: 4;
    }

    /* -- Header 5: Cart within the main header bar -- */
    .header-5 .site-header input[type="search"] {
        height: 55px;
    }
    .header-5 .site-header .site-header-cart {
        display: block;
        height: 55px;
        margin-left: 35px;
    }
    .header-5 .dmrthema-cart a.cart-contents {
        height: 55px;
        padding: 0 15px;
        border: 1px solid #eee;
        border-radius: 2px;
        line-height: 55px;
    }
    .header-5 .dmrthema-cart a.cart-contents:hover {
        border-color: #e2e2e2;
    }
    .header-5 .col-full-nav .site-header-cart {
        display: none;
    }
    .header-5 .site-header .main-navigation {
        width: 100%;
    }
    .header-5 .dmrthema-cart .cart-contents .amount {
        font-size: 14px;
        display: contents;
    }
    .header-5 .dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count {
        margin-right: -10px;
        color: #111;
        background-color: #eee;
    }
}
/* -- Header 4: One row header -- */
@media (min-width: 1199px) {
    .header-4 .primary-navigation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        text-align: center;
    }
}
@media (min-width: 993px) {
    .header-4 {
        overflow-x: hidden;
    }
    .header-4 .header-4-container {
        position: relative;
        z-index: 99;
        padding: 0 30px;
        border-bottom: 1px solid #eee;
        box-shadow: 0 3px 15px -5px rgba(0, 0, 0, 0.08);
    }
    .sticky-d.header-4 .header-4-container {
        position: sticky;
        top: 0;
    }
    .admin-bar.sticky-d.header-4:not(.woocommerce-checkout) .header-4-container {
        top: 32px;
    }
    .admin-bar.sticky-d.header-4.single-product .header-4-container {
        top: 0px;
    }
    /* Unstick if the top sticky bar is chosen on single products */
    .single-product.sticky-t.sticky-d.header-4 .header-4-container {
        position: relative;
    }
    .admin-bar.single-product.sticky-t.sticky-d.header-4 .header-4-container {
        top: 0px;
    }
    .header-4 .header-4-inner {
        display: flex;
        width: 100%;
        align-items: center;
    }
    .header-4 .site-header {
        flex: 0 0 auto;
        margin-right: 30px;
    }
    
    /* -- Full width -- */
    .full-width-header.header-4 .header-4-container {
        display: flex;
    }
    .header-4:not(.full-width-header) .header-4-inner {
        display: flex;
        align-items: center;
        max-width: 1170px;
        margin-right: auto;
        margin-left: auto;
        height: 100%;
    }
    .header-4 .header-4-container .main-header.col-full {
        padding: 0;
        max-width: inherit;
    }
    .header-4 .site-search {
        display: none;
    }
    .header-4 .site-header-cart {
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }
    .header-4 .dmrthema-cart .cart-contents {
        height: auto;
    }
    .header-4 .dmrthema-cart .cart-contents,
    .header-4 .search-trigger {
        position: relative;
        z-index: 9;
    }
    .header-4 .search-trigger,
    .header-4 .search-trigger:focus,
    .header-4 .search-trigger:hover {
        background: none;
        cursor: pointer;
    }
    .header-4 .search-trigger svg {
        width: 18px;
        height: 18px;
        margin: -4px 0px;
    }
    .header-4 .search-trigger span {
        padding: 8px 10px 8px 0;
        margin-right: 10px;
        border-right: 1px solid #eee;
        line-height: inherit;
        font-size: 14px;
        pointer-events: none;
    }
    .header-4 .dmrthema-myaccount {
        margin-left: -6px;
        padding-right: 20px;
    }
    .header-4 .dmrthema-myaccount a {
        display: flex;
        height: 100%;
        align-items: center;
        position: relative;
    }
    .header-4 .dmrthema-myaccount svg {
        width: 24px;
        height: 24px;
    }
}
/* -- Header 4 and 5: Search -- */
@media (max-width: 1060px) and (min-width: 993px) {
    body:not(.header-4):not(.header-5) .site-header .site-search {
        width: 100px;
    }
}
@media (min-width: 993px) and (max-width: 1155px) {
    .header-5 .site-header .site-search {
        width: 50px;
    }
}
/* -- Minimal Checkout Header 2,3 -- */
@media (min-width: 993px) {
    .woocommerce-checkout.min-ck.header-2:not(.woocommerce-order-received) .site-header .site-branding,
    .woocommerce-checkout.min-ck.header-3:not(.woocommerce-order-received) .site-header .site-branding {
        position: relative;
        left: auto;
        order: 0;
        transform: none;
    }
}
/* -- Minimal Checkout Header 4 -- */
@media (min-width: 993px) {
    .sticky-d.header-4.woocommerce-checkout.min-ck:not(.woocommerce-order-received) .header-4-container {
        position: relative;
    }
}
.sticky-d.woocommerce-checkout.min-ck.header-4:not(.woocommerce-order-received) .search-trigger {
    display: none;
}
.woocommerce-checkout.min-ck.header-4:not(.woocommerce-order-received) .site-header {
    margin: 0;
    border: none;
    width: 100%;
}
.header-4:not(.woocommerce-order-received) .woocommerce-checkout.min-ck .site-header {
    border: none;
    width: 100%;
}
/* Ajax Search for WC - https://wordpress.org/plugins/ajax-search-for-woocommerce/ */
.dgwt-wcas-no-submit .dgwt-wcas-ico-magnifier {
    z-index: 1;
}
/*
Header Mobile
========
*/
@media (max-width: 992px) {
    .main-header {
        position: relative;
    }
    .site-branding {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 70px;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    .site-header .custom-logo-link img {
        width: auto;
    }
    .site-branding p {
        margin: 5px 0 -3px 0;
        font-size: 11px;
        line-height: 1.22;
        display: none;
    }
    .site-header .dmrthema-cart a.cart-contents .amount {
        display: none;
    }
    .site-branding button.menu-toggle {
        position: absolute;
        left: 15px;
        width: 60px;
        height: 30px;
        padding: 0;
        background-color: transparent;
        display: block;
    }
    .site-branding button.menu-toggle:hover {
        background-color: transparent;
    }
    .menu-toggle .bar {
        display: block;
        position: absolute;
        top: calc(50% - 1px);
        z-index: 0;
        width: 22px;
        height: 2px;
        opacity: 1;
        background-color: #222;
        transition: transform 0.15s ease-in 0s, margin 0.15s ease-in 0.2s, opacity 0s ease-in 0.15s;
        transform: rotate(0deg);
        pointer-events: none;
    }
    .menu-toggle .bar:nth-child(1) {
        margin-top: -7px;
    }
    .menu-toggle .bar:nth-child(2) {
        margin-top: -1px;
    }
    .menu-toggle .bar:nth-child(3) {
        margin-top: 5px;
    }
    .menu-toggle .bar-text {
        position: absolute;
        top: 0;
        left: 0;
        margin-top: 8px;
        margin-left: 28px;
        font-size: 10px;
        font-weight: 600;
        letter-spacing: 0.03em;
        pointer-events: none;
    }
    button.mobile-search-toggle {
        position: absolute;
        top: 0px;
        right: 60px;
        height: 100%;
        padding: 0;
        display: flex;
        align-items: center;
        cursor: pointer;
        background-color: transparent;
    }
    .mobile-search-toggle.with-myaccount-icon {
        right: 95px;
    }
    .mobile-search-toggle svg {
        width: 20px;
        height: 20px;
        pointer-events: none;
    }
    .dmrthema-myaccount {
        position: absolute;
        top: 0px;
        right: 60px;
        height: 100%;
        font-size: 17px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    .dmrthema-myaccount a {
        display: inline-block;
        line-height: 1;
    }
    .dmrthema-myaccount svg {
        width: 24px;
        height: 24px;
    }
    .site-header .site-header-cart {
        position: absolute;
        z-index: 2;
        right: 15px;
    }
    .dmrthema-cart-icon {
        position: relative;
        top: -4px;
        right: -8px;
    }
     /* -- Hide the mobile menu and cart when the distraction free checkout option is selected -- */
    .min-ck.woocommerce-checkout button.menu-toggle,
    .min-ck.woocommerce-checkout .site-header .site-header-cart {
        display: none;
    }
    /* -- Visible search bar within header on mobile -- */
    .m-search-bh .site-header .col-full {
        padding-right: 0;
        padding-left: 0;
    }
    .m-search-bh .site-search,
    .m-search-toggled .site-search {
        display: block;
        padding: 0 1em 1em 1em;
        height: 60px;
        background-color: #fff;
        box-shadow: 0 3px 10px -5px rgba(0, 0, 0, 0.18);
        position: relative;
        z-index: 1;
    }
    .m-search-toggled .site-header .site-search {
        margin-left: -1em;
        margin-right: -1em;
    }
    .m-search-bh:not(.woocommerce-checkout.min-ck) .site-header .col-full {
        margin-bottom: 60px;
    }
    /* -- Mobile sticky -- */
    body:not(.mobile-toggled).sticky-m .site-header {
        position: sticky;
        z-index: 9;
        top: 0;
        box-shadow: 0 1px 15px rgba(0, 0, 0, 0.08);
    }
}
@media (max-width: 480px) {
    .site .site-branding p {
        max-width: 240px;
        line-height: 1.2;
    }
}
/*
Footer
========
*/
.below-content .col-full,
footer .col-full {
    display: flex;
    padding-right: calc(2.617924em - 20px);
    padding-left: calc(2.617924em - 20px);
}
/* -- Below content -- */
.below-content {
    clear: both;
    overflow: hidden;
    padding-top: 2rem;
    padding-bottom: 1.7em;
    border-top: 1px solid #eee;
}
.below-content .widget .widget-title {
    margin-bottom: 0;
    padding-left: 32px;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
    font-weight: 600;
    line-height: 1.35;
    text-transform: none;
    letter-spacing: 0;
}
.below-content .widget .textwidget p {
    margin-bottom: 0;
    padding-left: 32px;
    font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
    line-height: 1.4;
    margin-top: 0.15rem;
}
.below-content .widget,
footer .widget {
    position: relative;
    margin: 0;
    padding: 0 20px;
    flex: 1;
}
.below-content .widget .ri,
.below-content .widget svg {
    position: absolute;
    top: 3px;
    left: 20px;
}
.below-content .widget .ri {
    color: #999;
    font-size: 16px;
}
.below-content .widget svg {
    stroke: #dc9814;
    width: 20px;
    height: 20px;
}
.below-content .widget svg path {
    stroke-width: 1.5px;
}
/* -- Footer -- */
footer {
    clear: both;
}
.site-footer .widget .widget-title {
    margin-bottom: 12.5px;
}
footer .widget li {
    margin-bottom: 5px;
}
footer a:not(.button):hover {
    color: #fff;
}
footer.site-footer a:has(svg) {
    margin-right: 15px;
}
footer.site-footer a:hover,
footer.copyright a:hover {
    text-decoration: underline;
    text-decoration-thickness: 0.5px;
    text-underline-offset: 0.16em;
}
footer .review {
    font-size: 14px;
}
footer .review strong {
    font-size: 12px;
}
.site-footer {
    padding: 4rem 0;
}
.site-footer .widget p,
.site-footer .widget li {
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
    line-height: 1.5;
}
footer.copyright .widget:last-child {
    text-align: right;
}
footer.copyright .widget p {
    margin: 0;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}
footer.copyright .widget:last-child img {
    width: auto;
    height: 28.5px;
    margin: 0;
}
footer.copyright {
    padding: 2rem 0;
}
footer:last-child:not(:only-of-type) {
    margin-top: -3rem;
}
footer.copyright .col-full {
    align-items: center;
}
.site-footer .widget_nav_menu a {
    display: inline-flex;
}
.site-footer .widget_nav_menu .icon-wrapper {
    order: -1;
}
.site-footer .widget_nav_menu .icon-wrapper svg {   
    width: 22px;
    min-width: 22px;
    margin-right: 8px;
}
.site-footer .widget_nav_menu ul ul {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 14px;
}
.site-footer .icon-wrapper:empty {
    min-width: auto;
    margin: 0;
}
@media (max-width: 992px) {
    .below-content .widget .ri,
    .below-content .widget svg {
        left: 0;
    }
    .below-content .col-full,
    footer .col-full {
        display: block;
        padding-right: 1em;
        padding-left: 1em;
    }
    .below-content .widget {
        padding: 0;
        margin-bottom: 15px; 
    }
    .below-content {
        padding-bottom: calc(1.7em - 15px);
    }
    .below-content .col-full,
    .site-footer .col-full {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        -moz-column-gap: 2rem;
             column-gap: 2rem;
    }
    .site-footer {
        padding: 2.5rem 0 1rem 0;
    }
    .site-footer .widget {
        padding: 0;
        margin-bottom: 1.25rem;
    }
    .site-footer.alignright,
    footer.copyright .widget:last-child img {
        display: block;
        float: none;
    }
    footer.copyright .widget {
        padding: 0;
        margin-bottom: 1rem;
    }
    footer.copyright .widget:last-child {
        margin-bottom: 0;
    }
}
@media (max-width: 768px) {
    .below-content .col-full,
    .site-footer .col-full {
        grid-template-columns: repeat(1, 1fr);
    }
}
/*
Page Templates
========
*/
@media (min-width: 993px) {
    .col-full {
        box-sizing: content-box;
        margin-right: auto;
        margin-left: auto;
        max-width: 1170px;
        padding-right: 2.617924em;
        padding-left: 2.617924em;
    }
    .col-full:after {
        display: block;
        clear: both;
        content: "";
    }
}
@media (max-width: 992px) {
    .col-full {
        padding-right: 1em;
        padding-left: 1em;
    }
}
/* -- In case it is contained -- */
.dmrthema-contained #page {
    margin-left: auto;
    margin-right: auto;
}
/* -- Page and Post Sidebars -- */
@media (min-width: 993px) {
    .right-page-sidebar .content-area,
    .right-post-sidebar .content-area,
    .right-sidebar .content-area {
        float: left;
    }
    .right-page-sidebar .widget-area,
    .right-post-sidebar .widget-area,
    .right-sidebar .widget-area {
        float: right;
    }
    .left-page-sidebar .content-area,
    .left-post-sidebar .content-area,
    .left-sidebar .content-area {
        float: right;
    }
    .left-page-sidebar .widget-area,
    .left-post-sidebar .widget-area,
    .left-sidebar .widget-area {
        float: left;
    }
    /* -- Search Results -- */
    .search .content-area {
        float: right;
    }
}
/* -- Default Page Template -- */
.page-template-default:not(.woocommerce-page) .entry-header {
    margin-bottom: 1.5rem;
}
@media (min-width: 993px) {
    .page-template-default .site-content {
        padding-bottom: 3rem;
    }
}
@media (max-width: 992px) {
    .page-template-default .site-content {
        padding-bottom: 1.5rem;
    }
}
/* -- Canvas and Blank Canvas Page Templates -- */
.page-template-template-canvas .site-content .col-full,
.page-template-template-blank-canvas .site-content .col-full,
.page-template-template-canvas .woocommerce-message,
.page-template-template-blank-canvas .woocommerce-message {
    max-width: inherit;
}
.page-template-template-canvas:not(.pdp-shortcode) .site-content .col-full,
.page-template-template-blank-canvas:not(.pdp-shortcode) .site-content .col-full {
    padding: 0;
}
.page-template-template-canvas .content-area,
.page-template-template-blank-canvas .content-area {
    width: 100%;
    clear: both;
}
/* -- Full Width Page -- */
.page-template-template-fullwidth-php .content-area {
    width: 100%;
}
/* -- Full Width Page: No Heading -- */
.page-template-template-fullwidth-no-heading .content-area {
    width: 100%;
}
/* -- PLP: Full Width or No Sidebar Content -- */
.post-type-archive-product.dmrthema-full-width-content .content-area,
.woocommerce.archive.no-woocommerce-sidebar .content-area {
    width: 100%;
}
@media (min-width: 993px) {
    /* -- Blog Sidebars -- */
    .left-archives-sidebar #secondary {
        float: left;
    }
    .right-archives-sidebar #secondary {
        float: right;
    }
    .right-archives-sidebar .content-area {
        float: left;
    }
    .left-archives-sidebar .content-area {
        float: right;
    }
    /* -- Full Width Blog -- */
    .archive.no-archives-sidebar .content-area,
    .blog.no-archives-sidebar .content-area {
        width: 100%;
    }
    /* -- WooCommerce Sidebars -- */
    .left-woocommerce-sidebar #secondary {
        float: left;
    }
    .left-woocommerce-sidebar .content-area {
        float: right;
    }
    .right-woocommerce-sidebar #secondary {
        float: right;
    }
    .right-woocommerce-sidebar .content-area {
        float: left;
    }
}
/* -- Hover Overlay -- */
.site-content {
    position: relative;
}
.site-content:focus {
    outline: none;
}
@media (min-width: 993px) {
    .site-content:after {
        display: block;
        visibility: hidden;
        position: absolute;
        z-index: 5;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        background: rgba(0, 0, 0, 0.7);
        content: "";
        transition: opacity 0.3s, visibility 0.3s;
        pointer-events: none;
    }
    /*
    .header-4 .site-content:after {
        z-index: 1;
    }
    */
    .site.overlay:has(.hover-intent) .site-content:after {
        visibility: hidden;
        opacity: 0;
    }
    .site.overlay.visible:has(.hover-intent) .site-content:after {
        visibility: visible;
        opacity: 1;
    }
    .site.overlay .site-content:after,
    .drawer-open:before {
        position: fixed;
        visibility: visible;
        opacity: 1;
        height: 100vh;
    }
    body:has(.site.overlay) .col-full.topbar-wrapper {
		position: relative;
		z-index: 20;
	}
    body:not(.header-4):not(.sticky-d):has(.site.overlay) .col-full-nav {
        position: relative;
        z-index: 6;
    }
}
/* -- Elementor -- */
.woocommerce-cart .elementor,
.woocommerce-checkout .ae_data.elementor,
.woocommerce-checkout .elementor {
    clear: both;
}
.elementor-template-full-width .archive-header + .col-full {
    max-width: none;
    padding-left: 0;
    padding-right: 0;
}
/* -- CPT: Size Guide -- */
.single-ckit_size_guide .content-area { 
    width: 100%;
    margin: 0 auto;
    padding-top: 2.5em;
    padding-bottom: 2.5em;
    float: none;
}
/*
PLP
========
*/
.woocommerce.archive .site-content {
    padding-bottom: 3rem;
}
.shop header.woocommerce-products-header {
    margin: 0;
    background-color: transparent;
}
h1.woocommerce-products-header__title {
    margin-bottom: 1.5rem;
}
/* -- Hide Shop title but retain h1 -- */
.shop.shop-heading .woocommerce-products-header {
    align-items: flex-start;
}
.shop:not(.shop-heading) .woocommerce-products-header__title {
    display: none;
}
/* -- PLP options -- */
.dmrthema-sorting {
    width: 100%;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}
.dmrthema-sorting .woocommerce-notices-wrapper {
    order: 1;
}
.dmrthema-sorting .woocommerce-ordering {
    margin-bottom: 0;
    display: inline-flex;
    order: 2;
}
.dmrthema-sorting .woocommerce-result-count {
    order: 3;
}
.dmrthema-sorting .woocommerce-pagination {
    order: 4;
}
.dmrthema-sorting.sorting-end {
    margin-top: 1em;
    margin-bottom: 0;
}
.dmrthema-sorting.sorting-end:empty {
    margin: 0;
}
.woocommerce-result-count {
    margin: 0;
    color: #111;
    font-size: 14px;
}
select.orderby {
  position: relative;
  height: auto;
  line-height: inherit;
  margin-bottom: 0;
  padding-left: 0;
  border: 0;
  color: #111;
  font-size: 14px;
  box-shadow: none;
  background-position: center right;
}
@media (min-width: 993px) {
    .woocommerce-ordering {
        margin-right: 1.5em;
    }
}
/* -- PLP Category Description -- */
header.woocommerce-products-header {
    display: flex;
    background-color: #efeee3;
    align-items: center;
    overflow: hidden;
    margin-bottom: 2em;
}
header.woocommerce-products-header:not(:has(> *)) {
    margin: 0;
}
header.woocommerce-products-header img.cg-cat-image {
    width: 100%;
    max-width: 100%;
}
header.woocommerce-products-header .term-description {
    padding: 2em;
}
.term-desc-np header.woocommerce-products-header .term-description {
    padding: 0;
}
.term-desc-np header.woocommerce-products-header {
    background-color: transparent;
}
header.woocommerce-products-header .term-description p:last-child,
.dmrthema-category-banner .taxonomy-description p:last-child {
    margin: 0;
}
@media (min-width: 993px) {
    .term-description {
        min-width: 50%;
    }
}
header.woocommerce-products-header img {
    width: 50%;
}
header.woocommerce-products-header .term-description+img.cg-cat-image,
header.woocommerce-products-header .term-description+noscript+img.cg-cat-image,
header.woocommerce-products-header .term-description+picture {
    min-width: 50%;
    max-width: 50%;
}
header.woocommerce-products-header .term-description+picture img {
    width: 100%;
}
@media (max-width: 992px) {
    header.woocommerce-products-header {
        flex-direction: column;
    }
    .site-main header.woocommerce-products-header img.cg-cat-image {
        width: 100%;
        max-width: 100%;
    }
    header.woocommerce-products-header .term-description+img.cg-cat-image,
    header.woocommerce-products-header .term-description+noscript+img.cg-cat-image,
    header.woocommerce-products-header .term-description+picture {
        max-width: 100%;
    }
    .yit-wcan-container {
        margin: 0 -0.5em;
    }
    .dmrthema-sorting .woocommerce-result-count {
        order: 1;
        clear: both;
        font-size: 12px;
    }
    form.woocommerce-ordering {
        margin: 0;
        width: 100%;
    }
    .dmrthema-sorting {
        clear: both;
        display: grid;
        grid-template-columns: auto auto;
        gap: 0 20px;
        align-items: baseline;
        margin-top: 15px;
        margin-bottom: 15px;
        font-size: 12px;
    }
    select.orderby {
        font-size: 12px;
        width: 100%;
    }
    .archive .woocommerce-notices-wrapper {
        grid-column: 1/-1;
    }
    .dmrthema-full-width-content select.orderby {
        width: 100%;
    }
    .term-description {
        order: 2;
    }
    .dmrthema-category-banner .col-full .taxonomy-description,
    header.woocommerce-products-header .term-description {
        font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
    }
    header.woocommerce-products-header .term-description {
        padding: 1.25rem;
        letter-spacing: 0;
    }
    /* -- YITH Fixes -- */
    .woocommerce.archive.yith-wcan-pro #secondary {
        transition: none;
    }
    .woocommerce.archive.yith-wcan-preset-modal-open #secondary {
        position: inherit;
    }
}
/* -- WooCommerce Full Width Category Banner -- */
.dmrthema-category-banner {
    position: relative;
    padding: 2rem 0;
    background-size: cover;
    background-position: center;
    border-bottom: 1px solid #f1f1f1;
}
.dmrthema-category-banner .taxonomy-description p {
    margin-bottom: 15px;
}
.dmrthema-category-banner h1,
.dmrthema-category-banner .taxonomy-description {
    position: relative;
    color: #fff;
}
.dmrthema-category-banner .taxonomy-description {
    margin-bottom: 0;
}
.dmrthema-category-banner h1 {
    margin-bottom: 0.3em;
}
.dmrthema-category-banner h1:only-child {
    margin: 0;
}
.dmrthema-category-banner .term-description p:last-of-type {
    margin-bottom: 0;
}
.dmrthema-category-banner .taxonomy-description p a {
    transition: 0.2s all;
}
.dmrthema-category-banner .taxonomy-description ul {
    margin: 20px 0 0px 0;
    font-size: 12px;
    letter-spacing: 0.02em;
}
.dmrthema-category-banner .taxonomy-description ul li {
    display: inline-block;
    list-style: none;
    margin: 0 3px 7px 0px;
}
.dmrthema-category-banner .taxonomy-description ul li a {
    display: inline-block;
    background: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    text-transform: uppercase;
    font-weight: 600;
    transition: 0.2s all;
    border: 1px solid #f1f1f1;
    color: #111;
}
.dmrthema-category-banner .taxonomy-description ul li a:hover {
    background-color: rgba(255, 255, 255, 0.88);
    transform: translateY(-1.5px);
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.03);
}
.dmrthema-category-banner.lazy-background h1, 
.dmrthema-category-banner.lazy-background .taxonomy-description p,
.dmrthema-category-banner.lazy-background .taxonomy-description p a {
    color: #fff;
}
.dmrthema-category-banner.lazy-background .taxonomy-description p a:hover {
    color: #fff;
    opacity: 0.7;
}
.dmrthema-category-banner.lazy-background:before {
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}
@media (max-width: 992px) {
    .dmrthema-category-banner {
        padding: 1.5rem 0;
    }
}
/* -- Below Category Content -- */
.below-woocommerce-category {
    clear: both;
    margin: 2rem 0 0 0;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
    border: 1px solid #eee;
    padding: 1.5rem;
}
.below-woocommerce-category table tr td {
    border-bottom: 1px solid #eee;
    padding-left: 0;
}
.below-woocommerce-category p:last-child {
    margin: 0;
}
/* -- PLP Links -- */
.dmrthema-category-banner .taxonomy-description p a,
.below-woocommerce-category a,
.term-description a {
    text-decoration: underline;
    text-decoration-thickness: 0.75px;
    text-underline-offset: 0.18em;
}
/* -- Perfect Brands -- */
header.woocommerce-products-header .pwb-brand-banner img,
.pwb-brand-banner-cont.pwb-before-loop {
    width: 100%;
}
.pwb-brand-description {
    padding: 2em;
}
.pwb-before-loop {
    margin: 0;
}
.pwb-brand-description p:last-child {
    margin: 0;
}
/* -- Hide Drawer on Desktop -- */
@media (min-width: 993px) {
    .mobile-filter {
        display: none;
    }
}
/* -- Mobile PLP Filter Drawer -- */
@media (max-width: 992px) {
    .mobile-filter {
        display: block;
        position: sticky;
        z-index: 3;
        top: 0px;
        width: 100%;
        height: 40px;
        padding-left: 40px;
        background-color: #fff;
        border-top: 1px solid #e2e2e2;
        border-bottom: 1px solid #e2e2e2;
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.06);
        text-transform: uppercase;
        color: #111;
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 0.03em;
        line-height: 36px;
        text-align: left;
        margin-left: -1.3em;
        width: calc(100% + 2.6em);
    }
    .mobile-filter svg {
        position: absolute;
        left: 20px;
        width: 18px;
        height: 18px;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .page-template-template-woocommerce-archives .mobile-filter {
        margin-bottom: 25px;
    }
    .mobile-overlay {
        visibility: hidden;
        position: fixed;
        z-index: 101;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        background: rgba(0, 0, 0, 0.65);
        background: rgba(52, 53, 55, 0.4);
        content: "";
        transition: opacity 0.6s, visibility 0.6s;
    }
    .filter-open .mobile-overlay {
        visibility: visible;
        opacity: 1;
    }
    .mobile-toggled .mobile-overlay {
        top: 0px;
    }
    .close-drawer {
        color: #fff;
    }
    .filters.close-drawer {
        top: 40px;
        visibility: hidden;
        display: none;
    }
    .filter-open {
        overflow: hidden;
    }
    .filter-open .filters.close-drawer {
        position: fixed;
        visibility: visible;
        display: block;
        z-index: 299;
        top: 10px;
        left: 310px;
        width: 40px;
        height: 40px;
        background: transparent;
        padding: 0;
        cursor: pointer;
        transition: 0.3s all;
    }
    .woocommerce.archive #secondary,
    .wc-active.search-results #secondary,
    .page-template-template-woocommerce-archives #secondary {
        visibility: hidden;
        overflow-y: auto;
        position: fixed;
        z-index: 102;
        top: 0;
        left: -300px;
        width: 300px !important; /* Required */
        height: 100%;
        padding: 1.5rem;
        opacity: 0;
        background: #fff;
        -webkit-overflow-scrolling: touch;
        transition: all 0.35s;
        transition-delay: 0.1s;
    }
    .woocommerce.archive.filter-open #secondary,
    .wc-active.search-results.filter-open #secondary,
    .page-template-template-woocommerce-archives.filter-open #secondary,
    .woocommerce.archive.yith-wcan-preset-modal-open #secondary {
        visibility: visible;
        left: 0;
        opacity: 1;
    }
}
/*
Product Grid
========
*/
ul.products {
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + 30px);
    margin-left: -15px;
    margin-bottom: 0;
    clear: both;
}
ul.products li.product {
    position: relative;
    padding-right: 15px;
    padding-left: 15px;
    list-style: none;
}
@media (min-width: 993px) {
    .static-cta-buttons ul.products li.product.cgkit-swatch-hover,
    body:not(.static-cta-buttons) ul.products li.product {
        align-self: flex-start;
    }
}
@media (min-width: 993px) {
	ul.products li.product:not(.product-category) {
		padding-bottom: 40px;
	}
	ul.products li.product.product-category {
		margin-bottom: 2.5rem;
	}
	.columns-1 ul.products li.product,
	.upsells .columns-1 li.product,
	.related .columns-1 li.product,
	.cross-sells .columns-1 li.product {
		float: none;
		width: 100%;
		margin-right: 0
	}
	.columns-2 ul.products li.product,
	ul.products.columns-2 li.product,
	.upsells .columns-2 li.product,
	.related .columns-2 li.product,
	.cross-sells .columns-2 li.product {
		float: left;
		width: 49.9%;
	}
	.columns-3 ul.products li.product,
	ul.products.columns-3 li.product,
	.upsells .columns-3 li.product,
	.related .columns-3 li.product,
	.cross-sells .columns-3 li.product {
		float: left;
		width: 33.3053%;
	}
	.columns-4 ul.products li.product,
	.upsells .columns-4 li.product,
	.related .columns-4 li.product,
	.cross-sells .columns-4 li.product {
		float: left;
		width: 24.97%;
	}
	.columns-5 ul.products li.product,
	.upsells .columns-5 li.product,
	.related .columns-5 li.product,
	.cross-sells .columns-5 li.product {
		width: 19.98%;
	}
	.columns-6 ul.products li.product,
	.upsells .columns-6 li.product,
	.related .columns-6 li.product,
	.cross-sells .columns-6 li.product {
		float: left;
		width: 16.647%;
	}
}
@media (max-width: 992px) {
	ul.products {
		display: grid;
		grid-template-columns: repeat(2, minmax(10px, 1fr));
		-moz-column-gap: 20px;
		     column-gap: 20px;
		row-gap: 30px;
		width: 100%;
		margin-left: 0;
    }
    ul.products li.product {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        padding: 0;
    	margin-bottom: 0;
    }
    ul.products li.product .button {
    	margin-top: auto;
    }
    .elementor-shortcode ul.products {
        padding-left: 5px;
        padding-right: 5px;
    }

    .mobile-scroll ul.products {
    	width: calc(100% + 25px);
    }
}
@media (max-width: 600px) {
	.m-grid-1 ul.products {
		grid-template-columns: repeat(1, minmax(10px, 1fr));
	}
	.m-grid-1 ul.products li.product {
		width: 100%;
	}
}
/*
Product Carousel
========
*/
@media (max-width: 992px) {
    .related.products ul.products,
    .upsells.products ul.products,
    .cross-sells ul.products,
    .m-grid-2 .cross-sells ul.products,
    .mobile-scroll ul.products {
        display: flex;
        overflow-x: scroll;
        flex-wrap: nowrap;
        -webkit-overflow-scrolling: touch;
        padding-top: 5px;
        padding-right: 1em;
        padding-bottom: 20px;
        margin-left: -15px;
        padding-left: 1.25em;
        scroll-snap-type: x mandatory;
        overscroll-behavior-x: contain;
    }
    .related.products ul.products,
    .upsells.products ul.products {
    	width: calc(100% + 1.95em);
    }
    .related.products ul.products li.product,
    .upsells.products ul.products li.product,
    .cross-sells ul.products li.product,
    .mobile-scroll ul.products li.product {
        display: flex;
        max-width: 320px;
        min-width: 320px;
        flex-direction: column;
        scroll-snap-align: center;
        padding: 0;
    }
    .m-grid-1 .related.products ul.products li.product,
    .m-grid-1 .upsells.products ul.products li.product,
    .m-grid-1 .cross-sells ul.products li.product,
    .m-grid-1 .mobile-scroll ul.products li.product {
        padding-left: 0;
        padding-right: 0;
    }
    .related.products ul.products li.product .woocommerce-card__header,
    .upsells.products ul.products li.product .woocommerce-card__header,
    .cross-sells ul.products li.product .woocommerce-card__header,
    .mobile-scroll ul.products li.product .woocommerce-card__header {
        box-shadow: 0 0 10px rgb(0 0 0/10%);
        padding: 15px;
    }
}
/*
Search Results
========
*/
.search-results .content-area {
    float: right;
}
.search-results article {
    display: flex;
    margin: 1.75em 0 0 0em;
    padding-top: 1.75em;
    padding-bottom: 0;
    border-top: 1px solid #eee;
    border-bottom: 0;
}
.search-results article a.image-result {
    max-width: 100px;
    margin-right: 1.5rem;
}
.search-results article h2 {
    margin-bottom: 0.5rem;
    font-size: clamp(1.25rem, 1.123rem + 0.4065vw, 1.375rem); /* 20-22 */
    font-weight: 600;
    letter-spacing: 0;
}
.search-results article h2 a {
    color: #222;
}
.search-results article,
.search-results article p {
    margin-bottom: 0.5rem;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}
.search-results article:last-of-type {
    margin-bottom: 2rem;
}
.search-results .content-area {
    margin-bottom: 2rem;
}
.search-results .no-results.not-found {
    margin-bottom: 4rem;
}
/* -- No Results -- */
.search-no-results .secondary-wrapper {
    display: none;
}
.search-no-results .content-area {
    width: 100%;
    float: none;
    margin-bottom: 4em;
}
.search-no-results form.search-form {
    position: relative;
    display: flex;
    justify-content: stretch;
}
.search-no-results form.search-form label {
    margin-right: 1rem;
}
.search-no-results form.search-form label,
.search-no-results form.search-form input[type="search"] {
    display: flex;
    width: 100%;
    height: auto;
}
/*
About
========
*/
.title-left h2, .title-right h2 {
	position: relative;
}
@media (min-width: 993px) {
	.title-left h2:after, .title-right h2:before {
		position: absolute;
		top: 20px;
		width: 100px;
		height: 1px;
		background-color: #ccc;
		content: "";
	}
	.title-left h2:after {
		right: -115px;
	}
	.title-right h2:before {
		left: -115px;
	}
}
/*
Below Header
========
*/
.header-widget-region {
    position: relative;
    color: #fff;
    background-color: #dc9814;
    text-align: center;
}
.header-widget-region .widget {
    margin: 0;
    padding: 1em 0;
}
.header-widget-region .widget p {
    margin: 0;
    font-size: clamp(0.75rem, 0.5938rem + 0.5vw, 0.875rem); /* 12-14 */
}
.header-widget-region a {
    text-decoration: underline;
}
@media (max-width: 768px) {
    .header-widget-region .widget {
        padding: 0.6em 0;
        font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
    }
}
/*
Breadcrumb
========
*/
.woocommerce-breadcrumb {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: clamp(0.75rem, 0.6547rem + 0.3049vw, 0.84375rem); /* 12-13.5 */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: #666;
}
@media (min-width: 993px) {
    .woocommerce-breadcrumb {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
}
.woocommerce-breadcrumb p {
    margin: 0;
}
.woocommerce-breadcrumb a,
.woocommerce-breadcrumb a:hover {
    color: #111;
}
.woocommerce-breadcrumb a:hover {
    text-decoration: underline;
    text-decoration-thickness: .5px;
    text-decoration-color: #111;
    text-underline-offset: 0.18em;
}
.woocommerce-breadcrumb a:first-of-type:before {
    display: none;
}
.woocommerce-breadcrumb .breadcrumb-separator {
    display: inline-block;
    position: relative;
    top: -1px;
    padding: 0 0.8em;
    opacity: 0.4;
    font-size: 0.8em;
}
.woocommerce-breadcrumb.yoast .breadcrumb-separator {
    margin: 0 -1px;
}
/* -- PDP Breadcrumb -- */
.single-product .woocommerce-breadcrumb {
    max-width: 1170px;
    margin-right: auto;
    margin-left: auto;
    padding-right: 2.617924em;
    padding-left: 2.617924em;
}
@media (min-width: 993px) and (max-width: 1480px) {
    .single-product .woocommerce-breadcrumb {
        padding-left: calc(2.617924em + 5px);
    }
}
@media (max-width: 992px) {
    .single-product .woocommerce-breadcrumb {
        padding-right: 0;
        padding-left: 0;
    }
}
/* -- Breadcrumb off -- */
body:has(:not(.woocommerce-breadcrumb)) .site-content,
body.single-product:has(:not(.woocommerce-breadcrumb)) .site-main {
    padding-top: 2rem;
}
body:has(.woocommerce-breadcrumb) .site-content,
body.page-template-template-fullwidth-no-heading .site-content,
body.page-template-elementor_header_footer .site-content {
    padding-top: 0;
}
body.page-template-template-canvas:has(:not(.woocommerce-breadcrumb)) .site-content,
body.woocommerce-cart:has(:not(.woocommerce-breadcrumb)) .site-content,
body.woocommerce-checkout.min-ck:has(:not(.woocommerce-breadcrumb)) .site-content,
body.single-product:has(:not(.woocommerce-breadcrumb)) .site-content {
    padding-top: 0;
}
body.single-product:has(.woocommerce-breadcrumb) .site-main {
    padding-top: 0;
}
@media (max-width: 992px) {
    .archive:has(.mobile-filter):has(.dmrthema-category-banner) .archive-header:has(:not(.woocommerce-breadcrumb)) {
        padding-top: 0;
    }
    .single-product:has(:not(.woocommerce-breadcrumb)) .site-main {
        padding-top: 1em;
    }
}
/* -- Hide breadcrumb on certain templates -- */
.woocommerce-cart .woocommerce-breadcrumb,
.woocommerce-checkout .woocommerce-breadcrumb,
.single-ckit_size_guide .woocommerce-breadcrumb,
.single-post.post-l2 .woocommerce-breadcrumb {
    display: none;
}
/*
Buttons
========
*/
.added_to_cart.loading,
.button.loading,
.modal button.close:hover,
button.loading,
input[type=button].loading,
input[type=reset].loading,
input[type=submit].loading {
    opacity: 0.5;
}
.added_to_cart.disabled,
.added_to_cart:disabled,
.button.disabled,
.button:disabled,
button.disabled,
button:disabled,
input[type=button].disabled
,input[type=button]:disabled,
input[type=reset].disabled,
input[type=reset]:disabled,
input[type=submit].disabled,
input[type=submit]:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}
.added_to_cart.disabled:hover,
.added_to_cart:disabled:hover,
.button.disabled:hover,
.button:disabled:hover,
button.disabled:hover,
button:disabled:hover,
input[type=button].disabled:hover,
input[type=button]:disabled:hover,
input[type=reset].disabled:hover,
input[type=reset]:disabled:hover,
input[type=submit].disabled:hover,
input[type=submit]:disabled:hover {
    opacity: 0.35;
}
/* -- Loading spinner in button -- */
.single-product .product form.cart .single_add_to_cart_button:after,
.ajax_add_to_cart.add_to_cart_button:after {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -9px;
    margin-left: -9px;
    opacity: 0;
    transition: opacity 0s ease;
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-left-color: #fff;
    border-radius: 50%;
    vertical-align: middle;
}
.single-product .product form.cart .loading.single_add_to_cart_button:after,
.loading.ajax_add_to_cart.add_to_cart_button:after {
    opacity: 1;
    transition: opacity 0.25s ease;
    animation: rotate 450ms infinite linear;
}
.single-product div.product form.cart .button.loading,
.ajax_add_to_cart.add_to_cart_button.loading {
    color: transparent !important;
}
@media (max-width: 992px) {
    ul.products li.product .added_to_cart,
    ul.products li.product .button {
        opacity: 1;
    }
}
/* -- Lock icon -- */
#payment .place-order .button:before,
.cart-collaterals .checkout-button:before,
.widget_shopping_cart .buttons .checkout:before,
.wp-element-button.wc-block-cart__submit-button:before,
.wp-element-button.wc-block-components-button.wc-block-components-checkout-place-order-button:before {
    content: "";
    display: inline-flex;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    background-color: #fff;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' style='fill: rgba(0, 0, 0, 1);transform: ;msFilter:;'%3E%3Cpath d='M20 12c0-1.103-.897-2-2-2h-1V7c0-2.757-2.243-5-5-5S7 4.243 7 7v3H6c-1.103 0-2 .897-2 2v8c0 1.103.897 2 2 2h12c1.103 0 2-.897 2-2v-8zM9 7c0-1.654 1.346-3 3-3s3 1.346 3 3v3H9V7z'%3E%3C/path%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' style='fill: rgba(0, 0, 0, 1);transform: ;msFilter:;'%3E%3Cpath d='M20 12c0-1.103-.897-2-2-2h-1V7c0-2.757-2.243-5-5-5S7 4.243 7 7v3H6c-1.103 0-2 .897-2 2v8c0 1.103.897 2 2 2h12c1.103 0 2-.897 2-2v-8zM9 7c0-1.654 1.346-3 3-3s3 1.346 3 3v3H9V7z'%3E%3C/path%3E%3C/svg%3E");
    -webkit-mask-position: center;
            mask-position: center;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: contain;
            mask-size: contain;
}
.widget_shopping_cart .buttons .checkout:before {
    width: 14px;
    height: 14px;
    position: relative;
    top: 1px;
}
/*
Demo Store
========
*/
.demo_store {
    position: fixed;
    z-index: 9999;
    right: 0;
    bottom: 0px;
    left: 0;
    margin: 0;
    padding: 1em 1.41575em;
    color: #fff;
    background-color: #3d9cd2;
    font-size: 15px;
}
.demo_store a {
    color: #fff;
    font-weight: bold;
}
.demo_store a:hover {
    color: #fff;
}
.demo_store a.woocommerce-store-notice__dismiss-link {
    display: block;
}
@media (min-width:993px) {
    .demo_store {
        bottom: 0;
    }
    .demo_store a.woocommerce-store-notice__dismiss-link {
        display: inline-block;
        float: right;
        margin: -1em -1.41575em -1em 0;
        padding: 1em 1.41575em;
        background: rgba(0,0,0,.1)
    }
}
/*
Homepage
========
*/
/* -- Real Talk -- */
.image-border {
	position: relative;
}
.image-border img {
	position: relative;
	z-index: 1;
}
.image-border:after {
	position: absolute;
	top: 10px;
	right: -10px;
	width: 100%;
	height: 100%;
	opacity: 0.1;
	background-color: #dc9814;
	content: "";
}
/* -- Image CTA Features -- */
.image-feature a {
	overflow: hidden;
	position: relative;
}
.elementor-widget-image.image-feature a {
	display: block;
}
.image-feature .wp-caption {
	background: #000;
}
.image-feature a:before {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.45);
	content: "";
	transition: 0.15s ease-in-out;
	z-index: 2;
}
.image-feature a:hover:before {
	background-color: rgba(0, 0, 0, 0.65);
}
.image-feature img {
	position: relative;
	z-index: 1;
	width: 100%;
	min-height: 200px;
	transition: 0.25s ease-in-out;
	transform-origin: left top;
	-o-object-fit: cover;
	   object-fit: cover;
}
.image-feature a:hover img {
	transform: scale(1.1);
}
.image-feature figcaption {
	position: absolute;
	top: 0;
	padding: 2.5em 2em 0 2em;
	color: #fff;
	text-align: left;
	pointer-events: none;
	z-index: 2;
}
.image-feature figcaption strong,
.image-feature figcaption em,
.image-feature figcaption span {
	display: block;
	font-style: normal;
}
.image-feature figcaption strong {
	font-size: 24px;
	line-height: 1.55;
	letter-spacing: -0.3px;
}
.image-feature figcaption em {
	margin-bottom: 1.2em;
	font-size: 17px;
}
.image-feature figcaption span {
	display: inline-block;
	height: 40px;
	padding: 0 20px;
	border: 0;
	border-radius: 2px;
	color: #fff;
	background-color: #333;
	font-size: 14px;
	line-height: 40px;
	text-align: center;
	font-weight: 600;
}
@media (max-width: 992px) {
	.image-feature a,
	.image-feature a img {
		width: 100%;
	}
}
/*
Header Cart
========
*/
@media (min-width: 993px) {
    .site-header-cart {
        line-height: 60px;
        margin-left: auto;
    }
    .dmrthema-cart .cart-contents {
        padding: 0;
        color: #fff;
    }
    .dmrthema-cart a.cart-contents .count {
        position: relative;
    }
    .site-header .site-header-cart {
        display: none;
        width: auto;
    }
}
.dmrthema-cart .cart-contents .count {
    opacity: 1;
    font-size: 0.8em;
}
.dmrthema-cart a.cart-contents:hover .count {
    color: #fff;
    background-color: #dc9814;
}
.dmrthema-cart .cart-contents:after {
    margin-left: 10px;
}
.dmrthema-cart a.cart-contents .count {
    display: inline-block;
    position: relative;
    width: auto;
    min-width: 28px;
    height: 28px;
    margin-left: 5px;
    padding: 3px;
    border: 1px solid #dc9814;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    color: #dc9814;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
    letter-spacing: -0.7px;
    transition: all 0.2s;
}
.dmrthema-cart a.cart-contents .count:after {
    position: absolute;
    bottom: 90%;
    left: 50%;
    width: 10px;
    height: 6px;
    margin-bottom: 1px;
    margin-left: -6px;
    border: 1px solid #dc9814;
    border-bottom: 0;
    border-top-left-radius: 99px;
    border-top-right-radius: 99px;
    content: "";
    transition: all 0.2s;
    pointer-events: none;
}
.dmrthema-cart a.cart-contents:hover .count:after {
    bottom: calc(100% - 2px);
}
/* -- Cart icon -- */
.dmrthema-cart-icon svg {
    width: 22px;
    height: 22px;
    position: relative;
    top: 6px;
}
.dmrthema-cart-icon svg path {
    stroke-width: 1.5px;
}
.dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count {
    display: inline-block;
    position: relative;
    top: 5px;
    left: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #444;
    border: 1px solid #444;
    font-size: 8px;
    line-height: 18px;
    text-align: center;
    transition: 0.2s all;
}
.dmrthema-cart a.cart-contents:hover .dmrthema-cart-icon .mini-count {
    top: 2px;
}
.dmrthema-cart .cart-contents {
    display: block;
    text-decoration: none;
}
/*
Mini Cart
========
*/
.dmrthema-mini-cart-wrap {
    overflow: hidden;
    position: fixed;
    z-index: 102;
    top: 0;
    right: -420px;
    left: auto;
    width: 420px;
    height: 100%;
    background-color: #fff;
    transition: all 0.35s;
    outline: none;
}
@media (max-width: 480px) {
    .dmrthema-mini-cart-wrap {
        width: 100%;
        right: -100%;
    }
}
.admin-bar.customize-partial-edit-shortcuts-shown .dmrthema-mini-cart-wrap {
    top: 0;
}
.admin-bar.customize-partial-edit-shortcuts-shown .dmrthema-mini-cart-wrap .widget_shopping_cart {
    height: 100%;
}
.woocommerce-demo-store .dmrthema-mini-cart-wrap {
    z-index: 10000;
}
@media (min-width: 601px) and (max-width: 782px) {
    .admin-bar .dmrthema-mini-cart-wrap {
        top: 46px;
        height: calc(100% - 46px);
    }
    .admin-bar.sticky-m .header-4-container {
        top: 46px;
    }
}
@media (max-width: 782px) {
    .admin-bar.drawer-open {
        position: inherit;
    }
    .admin-bar .dmrthema-mini-cart-wrap {
        z-index: 99999;
    }
}
@media (min-width: 783px) {
    .admin-bar .dmrthema-mini-cart-wrap {
        top: 32px;
        height: calc(100% - 32px);
    }
}
/* -- Drawer opening and overlay -- */
.drawer-open {
    overflow: hidden;
    position: relative;
    height: 100%;
}
.drawer-open .dmrthema-mini-cart-wrap {
    overscroll-behavior: contain;
    right: 0;
}
.drawer-open .mobile-overlay {
    visibility: visible;
    opacity: 1;
}
@media (min-width: 993px) {
    .drawer-open:before {
        display: block;
        position: absolute;
        z-index: 100;
        top: 0;
        left: 0;
        width: 100%;
        height: 1000%;
        background: rgba(52, 53, 55, 0.5);
        content: "";
        cursor: pointer;
        transition: opacity 0.5s, visibility 0.5s;
    }
}
/* -- Loader -- */
.dmrthema-mini-cart-wrap div.blockOverlay {
    background-color: transparent !important;
}
#ajax-loading {
    position: absolute;
    z-index: 100;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}
.dmrthema-loader {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}
.spinner {
    width: 38px;
    height: 38px;
    border: 1px solid #ccc;
    border-bottom-color: #111;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 0.8s linear infinite;
}
/* -- Header -- */
.cart-drawer-heading {
    position: absolute;
    margin: 12px 20px;
    font-size: 17px;
    font-weight: 600;
    color: #111;
}
.dmrthema-mini-cart-wrap .close-drawer {
    display: block;
    position: absolute;
    top: 12.5px;
    right: 16px;
    width: 26px;
    height: 26px;
    color: #111;
    padding: 0;
    background: none;
}
.dmrthema-mini-cart-wrap .close-drawer:hover {
    background: none;
    color: #111;
}
.dmrthema-mini-cart-wrap .close-drawer span {
    display: flex;
    pointer-events: none;
    width: 26px;
    height: 26px;
}
/* -- Content -- */
.dmrthema-mini-cart-wrap .widget_shopping_cart {
    display: flex;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    z-index: 10;
    top: auto;
    left: auto;
    height: calc(100% - 50px);
    margin: 0;
    margin-top: 50px;
    padding: 0px 20px 0 20px;
    background: #fff;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart_content {
    display: flex;
    flex-direction: column;
    width: 100%;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart .product_list_widget {
    flex: 1;
    overflow-y: auto;
    width: auto;
    padding: 0 12px 0 0;
    overflow-x: hidden;
    scrollbar-color: gray transparent;
    scrollbar-width: thin;
}
.admin-bar .dmrthema-mini-cart-wrap .widget_shopping_cart .product_list_widget {
    height: calc(100vh - 275px);
}
.dmrthema-mini-cart-wrap .woocommerce-mini-cart.cart_list:before {
    display: inline-block;
    position: absolute;
    width: 100%;
    margin-left: -20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    content: "";
}
/* -- Remove icon -- */
.widget_shopping_cart .product_list_widget li a.remove {
    position: absolute;
    display: flex;
    top: 15px;
    left: 5px;
    font-size: 0;
    width: 14px;
    height: 14px;
}
.widget_shopping_cart .product_list_widget li a.remove:before {
    width: 14px;
    height: 14px;
    content: "";
    background-color: #444;
    display: inline-block;
    transition: opacity 0.2s ease;
}
.widget_shopping_cart .product_list_widget li a.remove:focus-visible {
    outline-offset: 1px;
}
/* -- Product -- */
.dmrthema-mini-cart-wrap .widget_shopping_cart li.mini_cart_item {
    overflow: hidden;
    position: relative;
    margin-bottom: 0;
    padding: 1em 0 1em 35px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding-left: 35px;
    font-size: 13px;
    line-height: 1.4;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart li:last-child {
    border: 0;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart li.mini_cart_item a {
    color: #222;
    margin-bottom: 0.25rem;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart .woocommerce-mini-cart-item del {
    text-decoration: none;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart .woocommerce-mini-cart-item del .amount {
    text-decoration: line-through;
}
.widget_shopping_cart .woocommerce-mini-cart-item .amount {
    color: #111;
    font-weight: bold;
}
.dmrthema-mini-cart-wrap .product_list_widget img {
    max-width: 55px;
    width: 55px;
    margin-left: 20px;
    margin-bottom: 0;
    float: right;
}
.dmrthema-mini-cart-wrap li.woocommerce-mini-cart-item a:hover {
    color: #111;
    text-decoration: none;
}
.dmrthema-mini-cart-wrap .widget_shopping_cart li.mini_cart_item dl.variation,
.dmrthema-mini-cart-wrap .widget_shopping_cart li.mini_cart_item dl.variation p {
    font-size: 12px;
    color: #111;
}
.dmrthema-mini-cart-wrap .product_list_widget li .quantity {
    display: block;
    color: #888;
    font-size: 12px;
    margin-top: 2px;
}
/* -- Totals -- */
.widget_shopping_cart .woocommerce-mini-cart__total strong {
    float: left;
}
.widget_shopping_cart .woocommerce-mini-cart__total .woocommerce-Price-amount {
    float: right;
}
.widget_shopping_cart p.total {
    margin: 0;
    padding: 1.2em 0 0 0;
    font-weight: bold;
    color: #111;
    font-size: 15px;
    border-top: 1px solid #e2e2e2;
}
.widget_shopping_cart p.total .tax_label {
    display: block;
    clear: both;
    text-align: right;
    font-weight: normal;
}
/* -- Buttons -- */
.widget_shopping_cart p.buttons {
    text-align: center;
    margin: 0;
    padding: 1em 0 1em 0;
}
.widget_shopping_cart .buttons .button {
    display: block;
}
.dmrthema-mini-cart-wrap .woocommerce-mini-cart__buttons a {
    height: 50px;
    padding-top: 0;
    padding-bottom: 0;
    font-weight: 400;
    line-height: 50px;
}
.widget_shopping_cart a.button {
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #333;
    background-color: #fff;
    transition: all 0.3s;
    outline-offset: -0.2rem;
}
.widget_shopping_cart a.button.checkout {
    font-size: 15px;
}
.widget_shopping_cart a.button:not(.checkout) {
    margin-bottom: 10px;
    font-size: 14px;
}
.widget_shopping_cart a.button:hover {
    background-color: #f8f8f8;
}
/* -- Content below -- */
.dmrthema-mini-cart-wrap .cart-drawer-below {
    font-size: 12px;
    line-height: 1.5em;
    padding: 0px 0px 1em 0px;
    text-align: center;
}
.dmrthema-mini-cart-wrap .cart-drawer-below img {
    max-width: 80%;
    margin: 2px auto 13px auto;
    display: block;
}
/* -- Empty mini cart -- */
.dmrthema-empty-mini-cart {
    font-size: 14px;
    text-align: center;
    border-top: 1px solid #e2e2e2;
}
.dmrthema-mini-cart-wrap .woocommerce-mini-cart__empty-message {
    position: relative;
    width: 100%;
    margin: 0;
    padding-top: 115px;
    border-top: 1px solid #eee;
    color: #222;
    font-size: 17px;
    font-weight: 600;
    text-align: center;
}
.dmrthema-mini-cart-wrap .woocommerce-mini-cart__empty-message:before {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin-top: 5px;
    margin-left: -10px;
    content: "";
    display: block;
    width: 100px;
    height: 100px;
    background: #d4d3d3;
}
.dmrthema-empty-mini-cart + p.woocommerce-mini-cart__empty-message {
    display: none;
}
.dmrthema-empty-mini-cart .widget {
    margin-bottom: 0;
}
.dmrthema-empty-mini-cart .widget:first-of-type {
    padding-top: 40px;
}
.dmrthema-empty-mini-cart svg {
    margin: 0px 0 10px 0;
    width: 90px;
    height: 90px;
}
.dmrthema-empty-mini-cart svg path,
.dmrthema-empty-mini-cart svg circle {
    fill: #ccc;
}
.dmrthema-empty-mini-cart h2,
.dmrthema-empty-mini-cart .heading {
    font-size: 20px;
    letter-spacing: 0;
    line-height: 1.4;
    font-weight: bold;
    color: #131315;
}
.dmrthema-empty-mini-cart ul {
    margin: 25px 0 15px 0;
}
.dmrthema-empty-mini-cart ul:not(.products) li a {
    position: relative;
    color: #111;
    display: block;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.45;
    background-color: #fff;
    transition: 0.2s all;
}
.dmrthema-empty-mini-cart ul:not(.products) li a:hover {
    border-color: #ccc;
}
/*
Navigation Desktop
========
*/
@media (min-width: 993px) {
    /* -- Hide mobile menu elements on desktop -- */
    .menu-primary-menu-container + .dmrthema-mobile-menu,
    body:not(.header-4) .col-full-nav .site-search,
    .main-navigation.toggled div.menu,
    .mobile-menu.close-drawer,
    .mobile-search-toggle,
    .menu-toggle,
    .mobile-extra {
        display: none;
    }
    /* -- Logo within Navigation -- */
    .logo-mark,
    .logo-mark img {
        display: inline-block;
        position: absolute;
    }
    .is_stuck .logo-mark {
        padding-right: 2em;
    }
    .menu-primary-menu-container {
        margin-left: 0;
        transition: all 0.55s;
    }
    .logo-mark {
        float: left;
        overflow: hidden;
        width: 0;
        margin-right: 0;
        transition: all 0.4s cubic-bezier(0.33, 0, 0.2, 1);
    }
    .logo-mark img {
        position: relative;
        top: -1px;
        vertical-align: middle;
    }
    /* -- Container -- */
    .col-full-nav {
        width: 100%;
        border-top: 1px solid transparent;
    }
    .dmrthema-primary-navigation {
        display: flex;
    }
    /* -- Sticky -- */
    .dmrthema-primary-navigation.is_stuck {
        position: fixed;
        z-index: 99;
        border-bottom: 1px solid #eee;
    }
    body:not(.header-4).sticky-d .col-full-nav {
        position: sticky;
        z-index: 10;
        top: 0;
    }
    body:not(.header-4).sticky-d .col-full-nav.is_stuck {
        z-index: 15;
        box-shadow: 0px 15px 10px -15px rgba(0, 0, 0, 0.1);
    }
    body:not(.header-4).sticky-d.admin-bar .col-full-nav.is_stuck {
        top: 32px;
    }

    /* -- Top level links -- */
    .main-navigation ul {
        margin: 0;
    }
    .main-navigation ul li {
        display: inline-block;
    }
    .menu-primary-menu-container > ul.menu:hover > li > a {
        opacity: 0.65;
    }
    .menu-primary-menu-container > ul.menu:hover > li:hover > a {
        opacity: 1;
    }
    .main-navigation ul li.menu-item > a {
        display: block;
    }
    .menu-primary-menu-container > ul > li > a span {
        position: relative;
    }
    .menu-primary-menu-container > ul > li > a {
        padding-right: 0.55em;
        padding-left: 0.55em;
        transition: all 0.2s;
    }
    .menu-primary-menu-container > ul > li:first-child > a {
        padding-left: 0;
    }
    .main-navigation ul.menu > li.menu-item-has-children {
        position: relative;
        text-align: left;
    }
    /* Underline animation on hover */
    .menu-primary-menu-container > ul > li > a span:before,
    .menu-primary-menu-container > ul > li.nolink > span:before {
        display: block;
        position: absolute;
        top: calc(100% + 2px);
        left: 0;
        width: 100%;
        border-bottom: 1px solid #ccc;
        content: "";
        transition: transform 0.6s cubic-bezier(0.28, 0.75, 0.22, 0.95);
        transform: scale(0, 1);
        transform-origin: right center;
    }
    .menu-primary-menu-container > ul > li.nolink > span:before {
        width: calc(100% - 18px);
    }
    .menu-primary-menu-container > ul > li:hover > a span:before,
    .menu-primary-menu-container > ul > li.nolink:hover > span:before {
        transform: scale(1, 1);
        transform-origin: left center;
    }
    /* -- Top level caret -- */ 
    .menu-primary-menu-container > ul > li.menu-item-has-children > a:after,
    .menu-primary-menu-container > ul > li.menu-item-has-children.nolink > span:after {
        display: inline-block;
        position: relative;
        top: 1px;
        margin-left: 6px;
        width: 12px;
        height: 12px;
        content: "";
        background-color: #fff;
        transition: transform 0.4s;
    }
    .main-navigation ul.menu > li.menu-item-has-children:hover > a:after,
    .main-navigation ul.menu > li.menu-item-has-children.nolink:hover > span:after {
        transform: rotate(180deg);
    }
    /* -- Nolink in the Top Level Menu -- */
    .main-navigation ul.menu > li.full-width.nolink {
        display: inline-block;
		padding-right: 0.55em;
        padding-left: 0.55em;			
    }
	.main-navigation ul.menu > li.nolink li {
        line-height: normal;
        padding-right: 0;
        padding-left: 0;
    }
	.main-navigation ul.menu > li.nolink > span {
		position: relative;
	}
	.main-navigation ul.menu > li.nolink > span.caret {
		display: none;
	}
    /* -- CTA Button in Top Menu -- */
    .menu-primary-menu-container > ul > li.menu-button > a span {
        padding: 6px 16px;
        border-radius: 30px;
        color: #fff;
        background: #444;
        font-size: 13px;
        font-weight: bold;
        transition: 0.2s background;
    }
    .menu-primary-menu-container > ul > li.menu-button > a {
        position: relative;
        top: -1px;
    }
    .menu-primary-menu-container > ul > li.menu-button > a span:before {
        display: none;
    }
    .menu-primary-menu-container > ul > li.menu-button > a:hover span {
        background: #555;
    }
    /* -- Regular dropdown -- */
    .main-navigation ul li:hover > .sub-menu-wrapper ul,
    .main-navigation ul li:focus-within > .sub-menu-wrapper ul {
        left: 0;
    }
    .main-navigation ul ul {
        position: absolute;
    }
    .main-navigation ul.menu > li:not(.full-width) > .sub-menu-wrapper {
        visibility: hidden;
        position: absolute;
        right: 0;
        left: 0;
        opacity: 0;
        transition: all 0.15s ease-in-out;
        transform: scale(0.8);
    }
    .main-navigation ul.menu > li.menu-item-has-children:hover > .sub-menu-wrapper,
    .main-navigation ul.menu > li.menu-item-has-children:focus-within > .sub-menu-wrapper {
        visibility: visible;
        z-index: 30;
        opacity: 1;
        transform: scale(1);
    }
    .main-navigation ul.menu > li:not(.full-width) ul.sub-menu {
        padding: 20px 12px;
        box-shadow: 0 1px 15px rgba(0, 0, 0, 0.08);
        line-height: 1.4;
        margin: 0;
        width: 200px;
        box-sizing: content-box;
    }
    /* -- Regular dropdown 2 columns -- */
    .main-navigation ul li.col-2 > .sub-menu-wrapper > .container > ul.sub-menu {
        -moz-column-count: 2;
             column-count: 2;
        width: 400px;
    }
    /* -- Regular dropdown 3 columns -- */
    .main-navigation ul li.col-3 > .sub-menu-wrapper > .container > ul.sub-menu {
        -moz-column-count: 3;
             column-count: 3;
        width: 600px;
    }
    /* -- Links -- */
    .main-navigation ul.menu > li:not(.full-width) a.sub-menu-link,
    ul.menu ul li.nolink {
        padding: 0.3em 0.8em;
    }
    /* -- Dropdown child menus -- */
    .main-navigation ul ul li {
        display: block;
        position: relative;
    }
    .main-navigation ul.menu > li:not(.full-width) ul li .sub-menu-wrapper {
        visibility: hidden;
        position: absolute;
        z-index: 1; /* Required */
        top: -20px;
        left: 220px;
        opacity: 0;
        transition: all 0.15s ease-in-out;
    }
    .main-navigation ul.menu > li:not(.full-width) ul li.menu-item-has-children > a:after {
        position: absolute;
        top: 0px;
        display: inline-block;
        right: 15px;
        width: 13px;
        height: 13px;
        content: "";
        background-color: #111;
        transform: translateX(50%) translateY(50%);
    }
    .main-navigation ul.menu > li:not(.full-width) ul > li:hover > .sub-menu-wrapper,
    .main-navigation ul.menu > li:not(.full-width) ul > li:focus-within > .sub-menu-wrapper {
        visibility: visible;
        position: absolute;
        top: -20px;
        left: 200px;
        opacity: 1;
    }
    /* -- Highlight Label -- */
    .main-navigation ul li a span strong {
        color: #fff;
        background-color: #222;
        font-size: 9px;
        height: 16px;
        line-height: 16px;
        padding: 0px 6px;
        display: inline-flex;
        margin-left: 7px;
        position: absolute;
        top: 2.5px;
        border-radius: 3px;
    }
    /* -- Full width dropdown -- */
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper {
        position: absolute;
        visibility: hidden;
        left: 0;
        width: 100%;
        opacity: 0;
        background: #fff;
        border-top: 1px solid #eee;
    }
    .main-navigation ul.menu li.full-width:hover > .sub-menu-wrapper,
    .main-navigation ul.menu li.full-width:focus-within > .sub-menu-wrapper {
        visibility: visible;
        opacity: 1;
    }
    .main-navigation ul.menu li.full-width ul li > .sub-menu-wrapper {
        position: relative;
        top: auto;
        right: auto;
    }
    .main-navigation ul.menu li.full-width {
        position: inherit;
    }
    .main-navigation ul li.full-width .container {
        max-width: 1170px;
        margin: 0 auto;
        padding: 0;
    }
    .main-navigation ul li.full-width ul {
        position: inherit;
    }
    .main-navigation ul li.full-width ul.products {
        display: block;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu {
        display: flex;
        justify-content: center;
        width: 100%;
        padding: 40px 0;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li {
        display: inline-block;
        vertical-align: top;
        float: none;
        width: 23%;
        padding-right: 25px;
        padding-left: 25px;
        border-left: 1px solid #eee;
        -moz-column-break-inside: avoid;
             break-inside: avoid;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li:last-child {
        border-right: 1px solid #eee;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu ul {
        width: 100%;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-2 {
        width: 40%;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-3 {
        width: 60%;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-4 {
        width: 80%;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-5 {
        width: 100%;
    }
    .main-navigation ul.menu li.menu-item-has-children.full-width > .sub-menu-wrapper li.menu-item-has-children > a:hover {
        color: inherit;
    }
    .main-navigation ul.menu > li.full-width li:not(.heading):not(.menu-item-has-children):not(.menu-item-image) > a.sub-menu-link:hover span:not(.caret),
    .main-navigation ul.menu > li.full-width li.menu-item-image > a.sub-menu-link:hover span.sub {
        text-decoration: underline;
        text-decoration-thickness: 0.5px;
        text-underline-offset: 0.18em;
    }
    /* Prevent column-count wrapping */
    .col-2 .sub-menu .sub-menu,
    .col-3 .sub-menu .sub-menu {
        overflow: hidden; /* fix for Firefox */
        -moz-column-break-inside: avoid;
             break-inside: avoid-column;
        -webkit-column-break-inside: avoid;
    }
    /* -- Heading -- */
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.menu-item-has-children > a,
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.heading > a,
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.nolink > span:not(.caret) {
        margin-bottom: 0.25rem;
    }
    .main-navigation ul.menu li.full-width li.heading {
        clear: both;
        padding-top: 15px;
    }
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li {
        margin-bottom: 0.25rem;
    }
    /* -- No link -- */
    .main-navigation ul.menu li.full-width li.nolink span:not(.caret) {
        display: block;
        padding-top: 0.15em;
        padding-bottom: 0.15em;
    }
    .main-navigation ul.menu li.full-width li.heading.nolink:first-child,
    .main-navigation ul.menu li.full-width li.heading.nolink:first-child span:not(.caret) {
        padding-top: 0;
    }
    /* -- Products within full width dropdown -- */
    .main-navigation ul.products li.product:not(.product-category):before {
        display: none;
    }
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.product:not(.product-category) {
        padding-bottom: 0;
    }
    .main-navigation ul.products li.product {
        padding: 0;
        width: 100%;
    }
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li a.button {
        width: 100%;
    }
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li a.button,
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.product a.added_to_cart {
        position: relative;
    }
    /* -- Divider in full width dropdown -- */
    .main-navigation ul.menu li.full-width ul li.divider {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    /* -- Columns within full width dropdown -- */
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-2 ul.sub-menu {
        -moz-column-count: 2;
             column-count: 2;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-3 ul.sub-menu {
        -moz-column-count: 3;
             column-count: 3;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-4 ul.sub-menu {
        -moz-column-count: 4;
             column-count: 4;
    }
    .main-navigation ul li.full-width > .sub-menu-wrapper > .container > ul.sub-menu > li.col-5 ul.sub-menu {
        -moz-column-count: 5;
             column-count: 5;
    }
    /* -- When hover intent theme option active -- */
    .main-navigation.hover-intent ul.menu li.menu-item-has-children.full-width:hover > .sub-menu-wrapper,
    .main-navigation.hover-intent ul.menu > li.menu-item-has-children:hover > .sub-menu-wrapper {
        display: none;
    }
    .main-navigation.hover-intent ul.menu li.menu-item-has-children.full-width.visible > .sub-menu-wrapper,
    .main-navigation.hover-intent ul.menu > li.menu-item-has-children.visible > .sub-menu-wrapper {
        display: block;
    }
    /* -- Images within full width dropdown -- */
    .main-navigation ul.menu ul li.menu-item-image span {
        font-size: 0px;
        display: block;
    }
    .main-navigation ul.menu ul li.menu-item-image img {
        margin-bottom: 0.5rem;
        border-radius: 4px;
        width: 100%;
    }
    .main-navigation ul.menu ul li.menu-item-image span.sub {
        font-size: 14px;
        text-align: center;
        font-weight: 600;
    }
    /* -- Header 4 Navigation -- */
    .header-4 .col-full-nav {
        width: auto;
        display: flex;
        margin-left: auto;
        background: transparent;
    }
    .header-4 .main-navigation ul.menu li > .sub-menu-wrapper {
        border-color: transparent;
    }
}
/*
Secondary Navigation
========
*/
.secondary-navigation {
    display: none;
}
@media (min-width: 993px) {
    .secondary-navigation {
        display: block;
        float: right;
        margin-left: auto;
        padding-left: 1.2em;
    }
    .secondary-navigation .menu {
        margin-right: -15px;
    }
    .secondary-navigation .sub-menu {
        display: none;
    }
    .secondary-navigation ul {
        margin: 0;
    }
    .secondary-navigation ul li {
        display: inline-block;
        position: relative;
    }
    .secondary-navigation ul li a {
        position: relative;
        display: block;
        padding: 0.7em 0.875em 0;
        color: #111;
        font-size: 14px;
    }
    .secondary-navigation .menu .ri a,
    .secondary-navigation .menu .fa a {
        padding-top: 50px;
    }
    .secondary-navigation .menu li:not(.ri) a {
        padding-top: 35px;
    }
    .secondary-navigation [class*=" ri-"],
    .secondary-navigation [class^=ri-] {
        -webkit-font-smoothing: inherit;
    }
    .secondary-navigation .icon-wrapper {
        position: absolute;
        top: 0px;
        left: 50%;
        top: 5px;
        transform: translate(-50%, 0%);
    }
    .secondary-navigation .icon-wrapper svg {
        width: 28px;
        height: 28px;
    }
    .secondary-navigation .icon-wrapper svg path {
        stroke-width: 1.5px;
    }
    .secondary-navigation .menu-item {
        padding: 0 5px;
        border-left: 1px solid #eee;
    }
    .secondary-navigation .menu-item:first-child {
        border: 0;
    }
}
/*
Navigation Mobile
========
*/
@media (max-width: 992px) {
    .col-full-nav .logo-mark,
    .col-full-nav .site-header-cart,
    .col-full-nav .dmrthema-myaccount,
    .col-full-nav .search-trigger {
        display: none;
    }
    /* -- Hide regular menu on mobile if the mobile menu is active -- */
    .menu-primary-menu-container:has(+ .dmrthema-mobile-menu) {
        display: none;
    }
    /* -- Structure -- */
    .col-full-nav .dmrthema-primary-navigation.col-full {
        padding: 0;
    }
    .main-navigation {
        padding-top: 1rem;
    }
    .main-navigation ul {
        list-style: none;
        margin: 0;
    }
    .main-navigation ul li {
        position: relative;
    }
    .menu-primary-menu-container > ul > li > a,
    .menu-primary-menu-container > ul > li.nolink {
        display: block;
        padding: 0.35rem 0;
    }
    .main-navigation ul.menu ul li {
        position: relative;
    }
    .main-navigation ul.menu ul li > a,
    .main-navigation ul.menu ul li.nolink {
        padding: 0.15rem 0;
        display: block;
    }
    .main-navigation ul.menu .sub-menu-wrapper {
        margin-top: 0.25rem;
        padding-bottom: 1rem;
        padding-left: 0.5rem;
    }
    .main-navigation ul.menu .sub-menu-wrapper .sub-menu-wrapper {
        margin-top: 0.25rem;
        padding-bottom: 0.5rem;
        padding-left: 0.5rem;
    }
    .menu-primary-menu-container > ul > li {
        transition: 0.2s all;
    }
    .main-navigation ul.menu > li.menu-item-has-children {
        position: relative;
    }
    .menu-primary-menu-container > ul > li.dropdown-open {
        margin-bottom: 0.75rem;
    }
    .main-navigation ul.menu .sub-menu-wrapper a.cg-menu-link {
        width: calc(100% - 40px);
    }
    /* -- Submenus -- */
    .main-navigation ul.menu li.menu-item-has-children .sub-menu-wrapper {
        position: fixed;
        left: -99999px;
        opacity: 0;
        transition: background-color 0.6s, transform 0.3s, opacity 0.3s;
        transform: translateX(-10px);
    }
    .main-navigation ul.menu li.menu-item-has-children:not(.dropdown-open) .sub-menu-wrapper {
        display: block;
    }
    .main-navigation ul.menu li.menu-item-has-children.dropdown-open > .sub-menu-wrapper {
        position: inherit;
        left: auto;
        opacity: 1;
        transform: translateX(0);
    }
    .main-navigation ul.menu > li.menu-item-has-children.dropdown-open > .sub-menu-wrapper {
        border-bottom: 1px solid #eee;
    }
    /* -- Search -- */
    .site-search {
        display: none;
    }
    .col-full-nav .site-search {
        display: block;
        padding-top: 20px;
        position: relative;
        z-index: 4;
    }
    /* -- Arrows -- */
    .main-navigation ul.menu li.menu-item-has-children span.caret {
        display: flex;
        position: absolute;
        z-index: 3;
        top: 0px;
        right: 0;
        width: 30px;
        cursor: pointer;
        justify-content: center;
        align-items: center;
    }
    .main-navigation ul.menu > li.menu-item-has-children.dropdown-open > span.caret,
    .main-navigation ul.menu li.menu-item-has-children li.menu-item-has-children.dropdown-open > span.caret {
        transition: transform 0.4s;
        transform: rotate(180deg);
    }
    .main-navigation ul.menu li.menu-item-has-children span.caret:after {
        display: inline-flex;
        width: 20px;
        height: 20px;
        background-color: #111;
        content: "";
    }
    .main-navigation ul.menu li.menu-item-has-children span.caret {
        height: 35px;
    }
    .main-navigation ul.menu li.menu-item-has-children li.menu-item-has-children span.caret {
        height: 27px;
        opacity: 0.5;
    }
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.menu-item-has-children > a + span.caret,
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.heading > a + span.caret {
        height: 24px;
    }
    /* -- Heading -- */
    .main-navigation ul.menu li.full-width > .sub-menu-wrapper li.heading > a.sub-menu-link {
        margin-top: 0.5rem;
    }
    /* -- Highlight Label -- */
    .main-navigation ul li a span strong {
        position: absolute;
        color: #fff;
        background-color: #222;
        font-size: 9px;
        height: 16px;
        line-height: 16px;
        padding: 0px 6px;
        display: inline-flex;
        margin-left: 7px;
        margin-top: 3.5px;
        border-radius: 2px;
    }
    /* -- Products -- */
    .main-navigation ul.products {
        grid-template-columns: repeat(1, minmax(10px, 1fr));
        padding-right: 0.5rem;
        margin-top: 1.15rem;
        margin-bottom: 1.5rem;
    }
    .main-navigation ul.products li.product .button,
    .main-navigation ul.products li.product .added_to_cart {
        margin-top: 0;
        opacity: 1;
    }
    /* -- Images -- */
    .main-navigation ul.menu ul li.menu-item-image {
        margin-top: 0.5rem;
        margin-bottom: 1rem;
    }
    .main-navigation ul.menu .sub-menu-wrapper li.menu-item-image a.cg-menu-link {
        width: 100%;
        padding-right: 0.5rem;
    }
    .main-navigation ul.menu ul li.menu-item-image a:hover {
        text-decoration: underline;
    }
    .main-navigation ul.menu ul li.menu-item-image span {
        font-size: 0px;
        display: block;
    }
    .main-navigation ul.menu ul li.menu-item-image img {
        margin-bottom: 0.5rem;
        border-radius: 4px;
        width: 100%;
    }
    .main-navigation ul.menu ul li.menu-item-image span.sub {
        font-size: 13px;
        text-align: center;
        font-weight: 600;
    }
    /* -- Mobile Extra -- */
    .mobile-extra {
        padding: 1rem 0 2rem 0;
    }
    .mobile-extra .widget {
        margin-bottom: 0;
    }
    .mobile-extra h2,
    .mobile-extra h4,
    .mobile-extra .heading {
        margin-top: 0;
        margin-bottom: 0.25rem;
        padding: 1rem 0 0 0;
        opacity: 0.6;
        font-size: 11px;
        letter-spacing: 1px;
        text-transform: uppercase;
        font-weight: normal;
    }
    .mobile-extra ul,
    .mobile-extra .widget p {
        margin: 0;
        padding: 0;
    }
    .mobile-extra ul li {
        margin-bottom: 0.35rem;
    }
    .mobile-extra a {
        font-size: 14px;
    }
    .mobile-extra .review {
        text-align: center;
        border-top: 1px solid #e2e2e2;
        padding-top: 30px;
        font-size: 14px;
        line-height: 1.5;
        margin: 30px 0 0 0;
    }
    .mobile-extra .review::before {
        display: block;
        font-size: 12px;
        letter-spacing: 3px;
        content: "\2605\2605\2605\2605\2605";
        margin-bottom: 5px;
        color: #ee9e13;
    }
    .mobile-extra .review strong {
        display: block;
        margin-top: 0.5rem;
        font-size: 11px;
        letter-spacing: 1px;
        text-transform: uppercase;
    }
    .mobile-extra .review .button {
        margin-top: 20px;
        display: block;
        font-weight: bold;
        color: #fff;
        transition: 0.2s background;
    }
    .col-full-nav .widget select {
        width: 100%;
        margin-top: 20px;
    }
    /* -- Slide out panel -- */
    .col-full-nav {
        position: fixed;
        z-index: 102;
        padding: 0 15px;
        top: 0;
        bottom: 0;
        height: 100%;
        left: -300px;
        width: 300px;
        min-height: 100vh;
        min-height: -moz-available;
        min-height: fill-available;
        min-height: -webkit-fill-available;
        max-height: 100vh;
        overflow-y: auto;
        overflow-x: hidden;
        background: #fff;
        overscroll-behavior: contain;
        -webkit-overflow-scrolling: touch;
        box-shadow: 0 0 10 rgba(0, 0, 0, 0.15);
        transition: all 0.35s;
        transition-delay: 0.1s;
    }
    .admin-bar .col-full-nav {
        z-index: 100000;
    }
    .mobile-toggled {
        overflow: hidden;
    }
    .mobile-toggled .col-full-nav {
        left: 0;
    }
    .mobile-toggled .mobile-overlay,
    .mobile-toggled .main-header:before,
    .mobile-toggled.m-search-bh .site-search:before {
        visibility: visible;
        opacity: 1;
    }
    /* -- Close 'X' -- */
    .mobile-menu.close-drawer {
        visibility: hidden;
        position: fixed;
        z-index: 999;
        top: 40px;
        left: 310px;
        padding: 0;
        opacity: 0;
        cursor: pointer;
        transition: 0.3s all;
    }
    .mobile-toggled .mobile-menu.close-drawer {
        visibility: visible;
        width: 40px;
        height: 40px;
        top: 10px;
        opacity: 1;  
    }
    /* -- Header 4 -- */
    .sticky-m .header-4-container {
        position: sticky;
        top: 0;
        z-index: 6;
    }
}
/*
Notices
========
*/
.woocommerce-message {
    max-width: 1170px;
    margin-bottom: 1em;
    background-color: #3bb54a;
    margin-right: auto;
    margin-left: auto;
}
.woocommerce-error,
.woocommerce-info,
.woocommerce-message,
.woocommerce-noreviews,
p.no-comments {
    clear: both;
    margin-bottom: 2rem;
    margin-left: 0;
    padding: 1em 1.618em;
    border-left: 0.4180469716em solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
    color: #fff;
    outline: 0;
}
.woocommerce-message .button:hover {
    opacity: 0.75;
}
ul.woocommerce-error li,
ul.woocommerce-info li {
    margin-bottom: 0;
}
.woocommerce-error a,
.woocommerce-info a,
.woocommerce-message a,
.woocommerce-noreviews a,
p.no-comments a {
    color: #fff;
    transition: 0.2s all;
}
.woocommerce-error a:hover,
.woocommerce-info a:hover,
.woocommerce-message a:hover,
.woocommerce-noreviews a:hover,
p.no-comments a:hover {
    opacity: 0.7;
    color: #fff;
}
.woocommerce-error a.button:hover,
.woocommerce-info a.button:hover,
.woocommerce-noreviews a.button:hover,
p.no-comments a.button:hover {
    opacity: 1;
}
.woocommerce-error .button,.woocommerce-info .button,
.woocommerce-message .button,
.woocommerce-noreviews .button,
p.no-comments .button {
    float: right;
    padding: 0 0 0 1em;
    border-width: 0 0 0 1px;
    border-left-style: solid;
    border-left-color: rgba(255, 255, 255, 0.25)!important;
    border-radius: 0;
    color: #fff;
    background: 0 0;
    box-shadow: none;
    line-height: 1.618;
    font-weight: 600;
}
.woocommerce-error .button:hover,
.woocommerce-info .button:hover,
.woocommerce-message .button:hover,
.woocommerce-noreviews .button:hover,
p.no-comments .button:hover {
    opacity: 0.7;
    color: #fff;
    background: 0 0;
}
.woocommerce-error pre,.woocommerce-info pre,
.woocommerce-message pre,
.woocommerce-noreviews pre,
p.no-comments pre {
    background-color: rgba(0, 0, 0, 0.1);
}
.woocommerce-error {
    list-style: none;
}
.woocommerce-error:focus-visible {
    outline: none;
}
/* -- Colors -- */
.woocommerce-info,
.woocommerce-noreviews,
p.no-comments {
    background-color: #3d9cd2;
}
.woocommerce-error {
    background-color: #e2401c;
}
.woocommerce-message .buttons-wrapper {
    display: none;
}
@media (min-width: 993px) {
    .woocommerce-message .message-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .woocommerce-message .message-content a.button {
        float: none;
        margin-left: auto;
        flex-shrink: 0;
    }
}
.woocommerce-message a.button:hover {
    opacity: 0.9;
    text-decoration: underline;
    text-decoration-thickness: 0.5px;
    text-underline-offset: 0.16em;
}
@media (max-width: 768px) {
    .woocommerce-message .message-inner {
        text-align: center;
    }
    .woocommerce-message .message-content a.button {
        display: block;
        padding-left: 0;
        border: none;
        float: none;
        margin: 0.5rem auto 0 auto;
        font-size: 13px;
    }
}
/* -- Cart Page Notices -- */
.woocommerce-cart .woocommerce > .woocommerce-error {
    margin-top: 1.5rem;
    margin-bottom: 0;
}
.woocommerce-cart .woocommerce-notices-wrapper {
    margin-bottom: 2rem;
}
.woocommerce-cart .woocommerce-notices-wrapper:empty {
    margin: 0;
}
.woocommerce-cart .woocommerce-notices-wrapper:not(:empty) + .checkout-wrap {
    margin-top: 2.5rem;
}
.woocommerce-cart:has(.checkout-wrap) .archive-header .woocommerce-message {
    margin-top: 1.5rem;
    margin-bottom: 0px;
}
@media (max-width: 992px) {
    .woocommerce-cart:has(.checkout-wrap) .archive-header .woocommerce-message {
        margin-bottom: 0.5rem;
    }
}
/*
Pagination
========
*/
.pagination .page-numbers {
    text-align: center;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}
.pagination ul.page-numbers {
    margin: 0;
    display: flex;
    align-items: center;
}
.pagination .page-numbers a {
    position: relative;
}
.pagination .page-numbers li {
    display: inline-block;
    padding: 10px;
}
.pagination .page-numbers li span,
.pagination .page-numbers li a {
    padding: 0;
}
.woocommerce-pagination .page-numbers li {
    display: inline-block;
    margin: 0 2px;
}
.pagination span.page-numbers,
.pagination a.page-numbers {
    padding: 10px;
}
.nav-links {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}
.search-results .nav-links:before,
.search-results .nav-links:after {
    flex: 1;
    content: '';
}
.nav-links:has(.prev):before,
.nav-links:has(.next):after {
  display: none;
}
.nav-links a.prev {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    padding-left: 0;
}
.nav-links a.next {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    padding-right: 0;
}
.woocommerce-pagination .page-numbers {
    margin: 0;
}
.woocommerce-pagination {
    font-size: 14px;
}
.product .woocommerce-pagination {
    text-align: center;
    margin-bottom: 2rem;
}
.woocommerce-pagination a,
.woocommerce-pagination a:hover {
    color: #111;
}
.woocommerce-pagination .next,
.woocommerce-pagination .prev {
    display: inline-block;
    position: relative;
    text-indent: -9999px;
}
.woocommerce-pagination .page-numbers.next,
.woocommerce-pagination .page-numbers.prev {
    padding-left: 0.6em;
    padding-right: 0.5em;
}
.page-numbers .next:after,
.page-numbers .prev:after,
.woocommerce-pagination .next:after,
.woocommerce-pagination .prev:after {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1em;
    height: 1em;
    content: "";
    background-color: #111;
    transform: translateX(-50%) translateY(-50%);
    text-rendering: auto;
    -webkit-mask-position: center;
            mask-position: center;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: contain;
            mask-size: contain;
}
.page-numbers .prev:after,
.woocommerce-pagination .prev:after {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.page-numbers .next:after,
.woocommerce-pagination .next:after {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E"); 
}
@media (min-width: 993px) {
    .woocommerce-pagination {
        margin-left: auto;
    }
    .woocommerce-pagination .page-numbers {
        padding: 0px 5px;
    }
}
@media (max-width: 992px) {
    .archive .woocommerce-pagination,
    .search-results .woocommerce-pagination {
        grid-column: 1/-1;
    }
    .archive .woocommerce-pagination,
    .search-results .woocommerce-pagination {
        order: 3;
    }
    .woocommerce-pagination {
        clear: both;
        text-align: center;
        margin-top: 20px;
    }
    .woocommerce-pagination ul.page-numbers {
        margin-bottom: 10px;
    }
    .woocommerce-pagination .page-numbers li {
        margin: 0 3px 6px 3px;
    }
    .woocommerce-pagination .page-numbers li .page-numbers {
        display: inline-block;
        width: 36px;
        height: 36px;
        font-size: 13px;
        margin: 0;
        padding: 0;
        border: 1px solid #dfdede;
        border-radius: 3px;
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.06);
        line-height: 36px;
    }
    .dmrthema-sorting .woocommerce-pagination {
        grid-column: span 2;
    }
}
/*
Product Card
========
*/
ul.products li.product:hover,
ul.products li.product:focus-within {
    z-index: 2;
}
/* -- Shadow on desktop -- */
@media (min-width: 993px) {
    ul.products li.product:not(.product-category):before {
        visibility: hidden;
        position: absolute;
        left: 0;
        opacity: 0;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        content: "";
        transition: all 0.2s;
        width: calc(100% + 0px);
        height: calc(100% + 30px);
        margin-top: -15px;
        margin-left: 0;
    }
}
/* -- Image -- */
ul.products li.product img {
    display: block;
    width: 100%;
    margin: 0;
    transition: all 0.2s ease-in-out;
}
li.product:not(.product-category):hover img {
    transform: scale(0.98);
}
/* -- Image Flip -- */
.woocommerce-image__wrapper {
    position: relative;
    transition: all 0.2s ease-in-out;
    width: 100%;
}
li.product:not(.product-category):hover .woocommerce-image__wrapper img {
    transform: none;
}
li.product:hover .woocommerce-image__wrapper {
    transform: scale(0.982);
}
.woocommerce-image__wrapper .gallery-image {
    position: absolute;
    z-index: -10;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.4s ease;
}
.woocommerce-image__wrapper picture.gallery-image {
    width: 100%;
    height: 100%;
}
li.product:hover .woocommerce-image__wrapper .gallery-image,
li.product:hover .woocommerce-image__wrapper .gallery-image img {
    top: 0;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
}
@media (min-width: 993px) {
    li.product:hover .woocommerce-image__wrapper .gallery-image,
    li.product:hover .woocommerce-image__wrapper .gallery-image img {
        z-index: 0;
        opacity: 1;
    }
    li.product:not(.product-category):hover .woocommerce-image__wrapper:has(.gallery-image) img:not(.gallery-image) {
        opacity: 0;
    }
}
@media (max-width: 992px) {
    li.product:hover .woocommerce-image__wrapper {
        transform: none;
    }
}
/* -- Header -- */
.woocommerce-card__header {
    clear: both;
    padding-top: 0.75rem;
}
.woocommerce-card__header * {
    position: relative;
}
@media (max-width: 992px) {
    li.product:not(.product-category) .woocommerce-card__header {
        display: flex;
        flex-direction: column;
        flex: 1;
    } 
}
/* -- Product Categories -- */
li.product p.product__categories,
li.menu-item-product p.product__categories {
    position: relative;
    margin-bottom: 0.25rem;
    -webkit-font-smoothing: antialiased;
    color: #444;
    line-height: 1.4;
    font-size: clamp(0.625rem, 0.5615rem + 0.2033vw, 0.6875rem); /* 10-11 */
    letter-spacing: 0.7px;
    text-transform: uppercase;
    margin-bottom: 0;
}
li.product p.product__categories a,
li.menu-item-product p.product__categories a {
    color: #444;
}
ul.products li.product.product-category h2 mark {
    display: none;
}
/* -- Product Title -- */
ul.products li.product .woocommerce-loop-product__title,
ul.products li.product h2 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
ul.products li.product .woocommerce-LoopProduct-link {
    display: block;
    color: #2c2d33;
    -webkit-backface-visibility: hidden;
}
@media (max-width: 770px) {
    .m-grid-2 ul.products li.product .woocommerce-loop-product__title {
        font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
    }
}
/* -- Price -- */
ul.products li.product .price {
    display: block;
    margin-bottom: 0.75rem;
    color: #111;
    font-size: clamp(0.75rem, 0.623rem + 0.4065vw, 0.875rem); /* 12-14 */
}
.price del {
    color: #72767c;
    margin-right: 0.25rem;
    font-size: 0.85em;
    font-weight: normal;
}
ul.products li.product .price ins {
    font-weight: bold;
}
ul.products li.product .price + .price {
    margin-top: -0.5rem;
}
ul.products li.product .price.wc-gzd-additional-info {
    margin-bottom: 0;
}
/* -- Star Rating -- */
ul.products li.product .star-rating {
    margin-bottom: 0.15rem;
    font-size: 0.8em;
}
/* -- Rating within card theme option -- */
.dmrthema_ratingContainer {
    display: flex;
    align-items: center;
    margin-bottom: 0.35rem;
}
ul.products li.product .dmrthema_ratingContainer .star-rating {
    margin-bottom: 0;
}
.dmrthema_ratingCount {
    font-size: 11px;
    margin-left: 5px;
    position: relative;
}
/* -- CTA Button -- */
ul.products li.product .button,
ul.products li.product .added_to_cart {
    display: block;
    position: absolute;
    z-index: 1;
    bottom: 0;
    width: calc(100% - 30px);
    height: 40px;
    padding: 0;
    border-radius: 4px;
    opacity: 0;
    font-size: clamp(0.75rem, 0.623rem + 0.4065vw, 0.875rem); /* 12-14 */
    font-weight: 600;
    line-height: 40px;
    text-align: center;
    transition: all 0.1s;
}
ul.products li.product:hover .added_to_cart,
ul.products li.product:focus-within .added_to_cart,
ul.products li.product:hover .button,
ul.products li.product:focus-within .button {
    opacity: 1;
}
@media (max-width: 992px) {
    ul.products li.product .button,
    ul.products li.product .add_to_cart_button,
    ul.products li.product .added_to_cart {
        position: relative;
        width: 100%;
        opacity: 1;
    }
}
@media (max-width: 770px) {
    ul.products li.product .button,
    ul.products li.product .added_to_cart {
        padding: 8px 12px;
        height: auto;
        line-height: inherit;
    }
}
/* -- Hide added button -- */
ul.products li.product .button.added {
    display: none;
}
/* -- Sale Badge -- */
li.product .onsale {
    position: absolute;
}
ul.products li.product .product-label {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    pointer-events: none;
}
.product-label {
    position: absolute;
    z-index: 1;
    top: -5px;
    left: 5px;
    min-width: 46px;
    max-width: 50%;
    min-height: 46px;
    align-items: center;
    padding: 5px 7px;
    border-radius: 35px;
    color: #fff;
    background-color: #3bb54a;
    font-size: 12px;
    letter-spacing: 0.4px;
    text-transform: uppercase;
    word-break: break-all;
}
li.product .onsale,
.product-label.type-bubble {
    top: 10px;
    left: 10px;
    z-index: 1;
    min-width: inherit;
    min-height: inherit;
    font-size: 11px;
    border-radius: 3px;
    padding: 3px 6px;
    line-height: 15px;
}
.product-details-wrapper .product-label {
    display: inline-block;
    position: relative;
    top: -1px;
    left: 0;
    min-width: inherit;
    min-height: inherit;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    padding: 3px 7px;
    line-height: 15px;
    margin-left: 10px;
}
.woocommerce-image__wrapper .product-label.type-circle {
    left: -10px;
}
/* -- Category Card -- */
ul.products li.product.product-category .cat-image-wrapper {
    overflow: hidden;
    display: block;
    position: relative;
}
ul.products li.product.product-category a:hover img {
    transform: scale(1.05);
}
ul.products li.product.product-category img {
    transform: scale(1);
    transition: transform 0.36s ease-out;
}
ul.products li.product:hover::before,
ul.products li.product:focus-within::before {
    visibility: visible;
    opacity: 1;
    transition: all 0.2s;
}
ul.products li.product.product-category a {
    display: block;
    color: #111;
}
ul.products li.product.product-category a:focus-visible {
    outline-offset: 12px;
}
ul.products li.product.product-category h2 {
    margin: 0.75em 0 0 0;
    font-size: clamp(0.9375rem, 0.8105rem + 0.4065vw, 1.0625rem); /* 15-17 */
    letter-spacing: 0;
    font-weight: normal;
}
/* -- Out of Stock -- */
.product-out-of-stock {
    position: absolute;
    z-index: 1;
    bottom: 0px;
    left: 0;
    padding: 0.5rem 0;
    width: 100%;
    font-size: 12px;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.85);
    text-align: center;
    pointer-events: none;
}
.product-out-of-stock em {
    font-style: normal;
}
/* -- Center Aligned Option -- */
.product-align-center ul.products li.product {
    text-align: center;
}
.product-align-center li.product .dmrthema_ratingContainer {
    justify-content: center;
}
.product-align-center ul.products li.product .star-rating {
    margin-right: auto;
    margin-left: auto;
}
.product-align-center ul.products li.product .dmrthema_ratingContainer > .star-rating {
    margin-left: inherit;
    margin-right: inherit;
}
/* -- Hide Buttons Option -- */
.no-cta-buttons ul.products li.product {
    align-self: flex-start;
}
.no-cta-buttons ul.products li.product:not(.product-category) {
    padding-bottom: 0;
    margin-bottom: 35px;
}
/* -- Static Buttons Option (Always Visible) -- */
.static-cta-buttons ul.products li.product:not(.product-category) {
    margin-bottom: 35px;
}
@media (max-width: 992px) { 
    .archive.static-cta-buttons ul.products {
        margin-bottom: 2rem;
    }
    .static-cta-buttons ul.products li.product:not(.product-category) {
        margin-bottom: 0px;
    }
}
.static-cta-buttons ul.products li.product .button,
.static-cta-buttons ul.products li.product .added_to_cart {
    opacity: 1;
}
/* -- Product Slide Card Option -- */
@media (min-width: 993px) {
    .product-card__slide ul.products li.product:not(.product-category):before {
        height: 100%;
        box-shadow: none;
    }
    .product-card__slide ul.products li.product:not(.product-category) {
        padding-bottom: 0px;
    }
    .product-card__slide ul.products li.product:not(.product-category):hover .woocommerce-card__header {
        transform: translateY(-40px);
        transition: transform 0.2s ease-out;
    }
    .product-card__slide .woocommerce-card__header {
        min-height: 110px;
        padding-top: 10px;
        background-color: #fff;
        transition: transform 0.2s ease-in;
    }
    .product-card__slide ul.products li.product .button,
    .product-card__slide ul.products li.product .added_to_cart {
        position: relative;
        width: 100%;
    }
}
/* -- Card Image Swipe on Mobile -- */
@media (max-width: 992px) {          
    .woocommerce-image__wrapper .gallery-image {
        position: relative;
        opacity: 1;
        z-index: inherit;
    }
    .dmrthema-plp-carousel-container {
        display: flex;
        width: 100%;
        height: 100%;
        overflow-x: scroll;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        scrollbar-width: none;
    }     
    .dmrthema-plp-carousel-container .dmrthema-plp-image-wrapper {
        display: flex;
        width: calc(200%); /* Ensure it is wide enough both images */
    }
    .dmrthema-plp-carousel-container img,
    .dmrthema-plp-carousel-container picture {
        width: 100%;
        height: 100%;
        scroll-snap-align: center;
        -o-object-fit: cover;
           object-fit: cover;
        flex-shrink: 0;
    }    
    .woocommerce-image__wrapper .dmrthema-plp-carousel--pagination {
        display: none;
    }  
    .woocommerce-image__wrapper:has(.gallery-image) .dmrthema-plp-carousel--pagination {
        display: block;
        z-index: 1;
    }
    li.product.outofstock .woocommerce-image__wrapper:has(.gallery-image) .dmrthema-plp-carousel--pagination,
    li.product.outofstock .gallery-image {
        display: none;
    }
    .dmrthema-plp-carousel--pagination {
        position: absolute;
        bottom: 5px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .dmrthema-plp-carousel--dot {
        height: 6px;
        width: 6px;
        margin: 0 2px;
        background-color: #fff;
        border: 1px solid #111;
        border-radius: 50%;
        display: inline-block;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    .dmrthema-plp-carousel--dot.active {
        background-color: #111;
    }        
}
/* -- Judge.me -- */
.jdgm-widget.jdgm-widget {
    position: relative;
}
/* -- Elementor editor only - hide duplicated product title -- */
.elementor-editor-active .woocommerce-loop-product__title + h2.woocommerce-loop-product__title {
    display: none;
}
/*
Quantity
========
*/
.quantity {
	position: relative;
}
.quantity .qty {
	text-align: center;
}
.quantity input::-webkit-outer-spin-button,
.quantity input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.quantity input[type=number] {
  -moz-appearance: textfield;
}
.quantity .minus,
.quantity .plus {
	height: 26px;
}
.quantity .minus:focus,
.quantity .plus:focus {
	outline: 0;
}
.quantity .minus:focus-visible,
.quantity .plus:focus-visible {
	z-index: 1;
	outline: 0.2rem solid #2491ff;
}
.quantity-nav {
	position: absolute;
	top: 0;
	right: -34px;
	bottom: 0;
	width: 35px;
	border: 1px solid #e2e2e2;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	background: #fff;
	box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
}
.quantity-button {
	position: absolute;
	right: 0;
	left: 0;
	height: 50%;
	background: no-repeat center;
	background-size: auto 8px;
	cursor: pointer;
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	transition: background-color 0.1s linear, opacity 0.1s linear;
}
.quantity-button.disabled {
	opacity: 0.25;
	cursor: default;
}
.quantity-button.quantity-down:before,
.quantity-button.quantity-up:before {
	content: "";
	width: 18px;
	height: 18px;
	position: absolute;
	left: 50%;
	top: 50%;
 	transform: translate(-50%, -50%);
	background: #333;
	-webkit-mask-position: center;
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
	-webkit-mask-size: contain;
	        mask-size: contain;
}
.quantity-button.quantity-up:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M14.7071 12.7071C14.3166 13.0976 13.6834 13.0976 13.2929 12.7071L10 9.41421L6.70711 12.7071C6.31658 13.0976 5.68342 13.0976 5.29289 12.7071C4.90237 12.3166 4.90237 11.6834 5.29289 11.2929L9.29289 7.29289C9.68342 6.90237 10.3166 6.90237 10.7071 7.29289L14.7071 11.2929C15.0976 11.6834 15.0976 12.3166 14.7071 12.7071Z' fill='%234A5568'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M14.7071 12.7071C14.3166 13.0976 13.6834 13.0976 13.2929 12.7071L10 9.41421L6.70711 12.7071C6.31658 13.0976 5.68342 13.0976 5.29289 12.7071C4.90237 12.3166 4.90237 11.6834 5.29289 11.2929L9.29289 7.29289C9.68342 6.90237 10.3166 6.90237 10.7071 7.29289L14.7071 11.2929C15.0976 11.6834 15.0976 12.3166 14.7071 12.7071Z' fill='%234A5568'/%3E%3C/svg%3E");
}
.quantity-button.quantity-down:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.29289 7.29289C5.68342 6.90237 6.31658 6.90237 6.70711 7.29289L10 10.5858L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L5.29289 8.70711C4.90237 8.31658 4.90237 7.68342 5.29289 7.29289Z' fill='%234A5568'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.29289 7.29289C5.68342 6.90237 6.31658 6.90237 6.70711 7.29289L10 10.5858L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L5.29289 8.70711C4.90237 8.31658 4.90237 7.68342 5.29289 7.29289Z' fill='%234A5568'/%3E%3C/svg%3E");
}
/* -- PDP -- */
.product .cart .quantity .input-text {
	width: 50px;
	height: 52px;
	padding: 0 0.4em;
	border: 1px solid #e2e2e2;
	background-color: #fff;
	outline-offset: -3px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}
.product .cart .quantity .input-text:focus-visible {
	position: relative;
	z-index: 1;
}
.product .cart .quantity .input-text:focus {
	border-color: #ccc;
}
.product .quantity .minus {
	bottom: 0px;
}
.quantity.hidden + .single_add_to_cart_button,
.quantity.hidden + button#ckwtl-button3,
.quantity.hidden + button#ckwtl-button3 + .single_add_to_cart_button,
.quantity:has(input[type=hidden]) + button#ckwtl-button3 + .single_add_to_cart_button  {
    margin-left: 0;
    width: 100%;
}
.product .quantity .plus:focus-visible,
.product .quantity .minus:focus-visible {
	outline-offset: -2px;
}
/* -- Minicart -- */
.dmrthema-custom-quantity {
    position: relative;
    display: inline-flex;
    margin: 0 4px;
}
.dmrthema-custom-quantity input[type=number]::-webkit-inner-spin-button,
.dmrthema-custom-quantity input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.dmrthema-custom-quantity  input[type=number] {
    -moz-appearance: textfield;
}
.dmrthema-custom-quantity input {
    width: 60px;
    height: 38px;
    line-height: 1.65;
    float: left;
    display: block;
    margin: 0;
    border: 1px solid #e2e2e2;
    padding: 0 30px 0 10px;
    text-align: center;
}
.dmrthema-custom-quantity input:focus {
    outline: 0;
}
.dmrthema-custom-quantity_quantity-nav {
    float: left;
    position: relative;
    height: 38px;
}
.dmrthema-custom-quantity-mini-cart {
    margin-top: 0.5rem;
    margin-bottom: 0.25rem;
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
    border: 1px solid #e1e0e0;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
    border-radius: 7px;
    width: 82px;
    padding: 2px;
    display: flex;
    align-items: center;
}
.dmrthema-custom-quantity-mini-cart input {
    width: 32px;
    height: 26px;
    border: none;
    text-align: center;
    font-size: 12px;
    pointer-events: none;
    color: #111;
    background: transparent;
    box-shadow: none;
}
.dmrthema-custom-quantity-mini-cart input::-webkit-outer-spin-button,
.dmrthema-custom-quantity-mini-cart input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.dmrthema-custom-quantity-mini-cart input[type="number"] {
    -moz-appearance: textfield;
}
.dmrthema-custom-quantity-mini-cart span {
    cursor: pointer;
    width: 26px;
    height: 26px;
    justify-content: center;
    display: flex;
    align-items: center;
}
.dmrthema-custom-quantity-mini-cart svg {
    height: 11px;
    width: 11px;
    stroke: #111;
}
/* -- Cart -- */
table.cart td.product-quantity .quantity .qty {
    z-index: 1;
}
table.cart td.product-quantity .quantity .quantity-nav {
	border: none;
	background-color: transparent;
	left: auto;
	right: auto;
	top: auto;
	bottom: auto;
	width: 100%;
	height: 31px;
	display: flex;
	align-items: center;
}
table.cart td.product-quantity .quantity .quantity-button {
	left: auto;
	right: auto;
	width: 26px;
	height: 25px;
	cursor: pointer;
	z-index: 2;
}
table.cart td.product-quantity .quantity .quantity-button.quantity-up {
	right: 0px;
}
table.cart .quantity-button.quantity-down:before {
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M18 12H6' /%3E%3C/svg%3E%0A");
	        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M18 12H6' /%3E%3C/svg%3E%0A");
}
table.cart .quantity-button.quantity-up:before {
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M12 6v12m6-6H6' /%3E%3C/svg%3E%0A");
	        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M12 6v12m6-6H6' /%3E%3C/svg%3E%0A");
}
table.cart .quantity .minus,
table.cart .quantity .plus {
	border: none;
	background-color: transparent;
	height: 31px;
}
table.cart .quantity-button.quantity-up {
	top: auto;
}
table.cart .quantity input {
	width: 32px;
	border: none;
	text-align: center;
	font-size: 12px;
	color: #111;
	background: transparent;
}
table.cart .quantity-button.quantity-down:before, 
table.cart .quantity-button.quantity-up:before {
	width: 13px;
	height: 13px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}
@supports (-webkit-overflow-scrolling: touch) {
  table.cart .quantity {
      width: 100px;
  }
	table.cart .quantity input {
	 font-size: 16px;
	}
}
/*
Top Bar
========
*/
.col-full.topbar-wrapper {
	max-width: 100%;
	border-bottom: 1px solid #eee;
}
.top-bar form,
.top-bar .widget {
	margin: 0;
}
.top-bar select {
	font-size: 13px;
	height: 26px;
	background-size: 12px 12px;
	background-position: calc(100% - 7px);
}
.top-bar .col-full {
	display: flex;
	padding: 0;
	align-items: center;
}
.top-bar p {
	margin: 0;
}
.top-bar a {
	transition: all 0.2s;
}
.top-bar a:hover {
	opacity: 0.85;
}
.top-bar svg {
	width: 20px;
	height: 20px;
	margin-right: 5px;
}
.top-bar-left {
	display: flex;
	flex: 1;
}
.top-bar-center {
	display: flex;
	justify-content: center;
	max-width: 45%;
	text-align: center;
}
.top-bar-center:only-child {
	width: 100%;
	max-width: inherit;
	text-align: center;
}
.top-bar-right {
	flex: 1;
	display: flex;
	justify-content: flex-end;
}
/* -- Menu widget -- */
.top-bar .widget_nav_menu {
	z-index: 30;
}
.top-bar .widget_nav_menu ul li {
	position: relative;
	z-index: 2;
	display: inline-block;
	margin: 0 7px 0 0;
}
.top-bar .widget_nav_menu ul > li > a {
	padding: 6px 0;
}
.top-bar .widget_nav_menu.top-bar-right ul li {
	margin: 0 0 0 7px;
}
.top-bar .widget_nav_menu ul li .sub-menu {
	display: none;
	position: absolute;
	margin-top: 6px;
	margin-left: -15px;
	background-color: #fff;
	padding: 15px 15px 11px 15px;
	box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.15);
}
.top-bar .widget_nav_menu ul li .sub-menu li {
	display: block;
	margin-bottom: 4px;
	font-size: 13px;
}
.top-bar .widget_nav_menu ul li:hover > .sub-menu {
	display: block;
	width: 150px;
}
.top-bar .widget_nav_menu ul li .sub-menu li a:hover {
	text-decoration: underline;
}
@media (max-width: 992px) {
	.topbar-wrapper .top-bar {
		padding: 0.6rem 0;
		font-size: clamp(0.75rem, 0.623rem + 0.4065vw, 0.875rem); /* 12-14 */
	}
	.mobile-toggled.drawer-open .top-bar {
		display: none;
	}
	.mobile-toggled .topbar-wrapper:before {
		visibility: visible;
		opacity: 1;
	}
	.top-bar {
		border-bottom: none;
	}
	.top-bar select {
		font-size: 13px;
	}
	.top-bar .col-full {
		padding: 0;
	}
}
@media (max-width: 768px) {
	.col-full.topbar-wrapper.hide-on-mobile {
		display: none;
	}
	.top-bar .col-full,
	.top-bar-left,
	.top-bar-center,
	.top-bar-right {
		display: block;
		text-align: center;
		max-width: 100%;
	}
	.top-bar select {
		margin-top: 5px;
		width: 100%;
	}
}
/*
Star Rating
========
*/
.star-rating {
    height: 1.118em;
    position: relative;
    width: 5.6em;
    overflow: hidden;
}
.star-rating:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z' fill='%234B5563'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z' fill='%234B5563'/%3E%3C/svg%3E");
    -webkit-mask-position: left;
            mask-position: left;
    -webkit-mask-repeat: repeat-x;
            mask-repeat: repeat-x;
    -webkit-mask-size: 1.118em;
            mask-size: 1.118em;
    background: #ccc;
    bottom: 0;
    content: "";
    display: block;
    height: 1.118em;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 5.6em;
}
.star-rating > span {
    display: block;
    text-indent: -10000px;
    width: 5.6em;
    height: 1.118em;
    position: absolute;
    overflow: hidden;
}
.star-rating > span:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z' fill='%234B5563'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.04893 2.92707C9.34828 2.00576 10.6517 2.00576 10.951 2.92707L12.0206 6.21886C12.1545 6.63089 12.5384 6.90985 12.9717 6.90985H16.4329C17.4016 6.90985 17.8044 8.14946 17.0207 8.71886L14.2205 10.7533C13.87 11.0079 13.7233 11.4593 13.8572 11.8713L14.9268 15.1631C15.2261 16.0844 14.1717 16.8506 13.3879 16.2812L10.5878 14.2467C10.2373 13.9921 9.76269 13.9921 9.4122 14.2467L6.61203 16.2812C5.82832 16.8506 4.77384 16.0844 5.07319 15.1631L6.14276 11.8713C6.27663 11.4593 6.12997 11.0079 5.77949 10.7533L2.97932 8.71886C2.1956 8.14946 2.59838 6.90985 3.5671 6.90985H7.0283C7.46153 6.90985 7.84548 6.63089 7.97936 6.21886L9.04893 2.92707Z' fill='%234B5563'/%3E%3C/svg%3E");
    -webkit-mask-position: left;
            mask-position: left;
    -webkit-mask-repeat: repeat-x;
            mask-repeat: repeat-x;
    -webkit-mask-size: 1.118em;
            mask-size: 1.118em;
    background-repeat: repeat-x;
    bottom: 0;
    content: "";
    display: block;
    height: 1.118em;
    left: 0;
    position: absolute;
    right: 0;
    text-indent: 10000px;
    top: 0;
}
p.stars {
    margin: 0;
}
p.stars,
p.stars a {
    display: inline-block;
}
p.stars a {
    position: relative;
    width: 1em;
    height: 1em;
    margin-right: 1px;
    font-weight: 400;
    text-decoration: none;
    text-indent: -999em;
    color: #000;
}
p.stars a:before {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 1em;
    height: 1em;
    font-family: sans-serif;
    line-height: 1;
    text-indent: 0;
}
p.stars a:before,
p.stars a:hover~a:before,
p.stars.selected a.active~a:before {
    color: #ccc;
    content: "\2605";
}
p.stars.selected a.active:before,
p.stars:hover a:before {
    opacity: 1;
    color: #96588a;
    content: "\2605";
}
p.stars.selected a:not(.active):before {
    opacity: 1;
    color: #96588a;
    content: "\2605";
}
/*
Widgets
========
*/
.widget .widget-title {
    display: block;
    margin-bottom: 0.65em;
    color: #111;
}
.widget_recent_reviews .widget-title,
.widget_recently_viewed_products .widget-title,
.widget_top_rated_products .widget-title,
.widget_products .widget-title {
    margin-bottom: 0;
}
.widget ul {
    margin-bottom: 0;
    margin-left: 0;
    list-style: none;
}
.widget ul ul {
    margin-top: 0.7em;
    margin-left: 1em;
}
.widget-area .widget .amount,
.widget_price_filter .price_slider_amount,
.widget_top_rated_products del span,
.widget_top_rated_products ins span {
    font-size: 12px;
}
.widget ins span.amount,
.widget ins span.amount span {
    margin-left: 2px;
}
.widget .star-rating {
    font-size: 11px;
    margin-top: 4px;
    margin-bottom: 2px;
}
.widget-area .widget .dmrthema_ratingContainer {
    margin-top: 3px;
    margin-bottom: 0;
}
.widget .dmrthema_ratingContainer .star-rating {
    margin-top: 0;
    margin-bottom: 0;
}
/* -- Sidebar specific -- */
@media (min-width: 993px) {
    .widget-area {
       padding-bottom: 2rem;
    }
}
@media (max-width: 992px) {
    .widget-area {
        padding-top: 2rem;
        padding-bottom: 1rem;
    }
}
#secondary .widget {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    flex-direction: column;
}
#secondary .widget:last-child {
    padding-bottom: 0;
    border: none;
}
#secondary > .widget:first-child .widget-title {
    padding-top: 0;
    border: 0;
}
/* -- Widget font sizes and links -- */
.widget li,
.site-content .widget li {
    margin-bottom: 0.4em;
    font-size: 14px;
    line-height: 1.45;
}
.widget-area .widget a {
    color: #222;
}
.widget-area .widget a:not(.button):hover {
    text-decoration: underline;
    text-decoration-thickness: 0.5px;
    text-underline-offset: 0.18em;
}
.widget .current-cat > a,
.widget .current-cat > a:hover {
    font-weight: 600;
}
/* -- Product List Widget -- */
.product_list_widget li {
    padding: 1em 0 1em 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.widget-area .product_list_widget li {
    padding-top: 0.7em;
}
.product_list_widget li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border: none;
}
.widget.product_list_widget li:first-child {
    padding-top: 0;
}
.product_list_widget li del {
    opacity: 0.75;
    font-size: 0.85em;
}
.product_list_widget li img {
    float: right;
    max-width: 2.617924em;
    margin: 3px 0 10px 10px;
}
.product_list_widget li > a {
    display: block;
}
.widget_products a {
    display: block;
}
.widget.product_list_widget a {
    font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
}
.widget.product_list_widget a:hover {
    color: #2c2d33;
}
/* -- Layered Navigation Filters -- */
.widget_layered_nav li {
    margin-bottom: 0.6180469716em;
}
.widget_layered_nav ul li {
    position: relative;
    padding-left: 20px;
}
.widget_layered_nav ul li:before {
    display: block;
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    box-shadow: 0px 0px 2px 0px #555;
    position: absolute;
    left: 0px;
    margin-top: 6px;
    background: #fff;
    border: 2px solid #fff;
}
.widget.woocommerce li .count {
    font-size: 12px;
    position: absolute;
    top: 2px;
    right: 0px;
    opacity: 0.75;
}
/* -- Price Filter -- */
.widget_price_filter .ui-slider {
    position: relative;
}
.widget.widget_price_filter {
    overflow: visible;
}
.widget_price_filter .ui-slider-horizontal {
    height: 0.25em;
}
.widget_price_filter .ui-slider .ui-slider-handle:last-child {
    margin-left: -0.6em;
}
.price_slider_wrapper {
    padding-top: 0.5em;
}
.widget_price_filter .price_slider_amount {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.widget_price_filter .price_slider_amount .clear {
    display: none;
}
.widget_price_filter .price_slider_amount .button {
    padding: 4px 11px;
    border-radius: 30px;
    color: #fff;
    background-color: #282828;
    font-size: 12px;
    font-weight: normal;
}
.widget_price_filter .price_slider {
    margin-bottom: 1.2em;
}
.widget_price_filter .ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    cursor: ew-resize;
    outline: 0;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    top: 0;
    border: 7px solid #fff;
    box-shadow: 0 0 3px 0 rgba(0,0,0,.15);
    margin-top: -7px;
}
.widget_price_filter .ui-slider .ui-slider-handle.ui-state-active,
.widget_price_filter .ui-slider .ui-slider-handle:hover {
    box-shadow: 0 0 0 0.236em rgba(0, 0, 0, 0.1);
}
.widget_price_filter .price_slider_wrapper .ui-widget-content,
.widget_price_filter .ui-slider .ui-slider-range {
    border: 0;
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    border-bottom-right-radius: 1em;
    border-bottom-left-radius: 1em;
}
.widget_price_filter .ui-slider .ui-slider-range {
    display: block;
    position: absolute;
    z-index: 1;
    background: #2c2d33;
    font-size: 0.7em;
}
.widget_price_filter .price_slider_wrapper .ui-widget-content {
    background: rgba(0, 0, 0, 0.1);
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range-min {
    left: -1px;
}
.widget_price_filter .ui-slider-horizontal .ui-slider-range-max {
    right: -1px;
}
/* -- Product Categories -- */
.widget.widget_product_categories li .count {
    right: -25px;
}
.widget_product_categories {
    padding-right: 25px;
}
.widget_product_categories ul li {
    position: relative;
}
.widget_product_categories ul .children,
.wc-block-product-categories-list.wc-block-product-categories-list--depth-1 {
    margin-top: 0.7em;
    margin-bottom: 0.7em;
    border-left: 1px solid #eee;
    margin-left: 0;
}
.widget_product_categories ul ul.children li,
.widget .wc-block-product-categories-list.wc-block-product-categories-list--depth-1 li {
    padding-left: 1.2em;
    font-size: 13px;
}
/* -- Average Rating -- */
.widget_rating_filter .wc-layered-nav-rating {
    margin-bottom: 0.6180469716em;
}
.widget_rating_filter .wc-layered-nav-rating a {
    font-size: 12px;
    text-decoration: none !important;
}
.widget.widget_rating_filter ul li.wc-layered-nav-rating {
    margin-bottom: 0.5em;
    font-size: 12px;
}
.widget_rating_filter .wc-layered-nav-rating .star-rating {
    display: inline-block;
    height: 1em;
    line-height: 1;
    margin-top: 0;
    margin-bottom: 0;
}
.widget.widget_rating_filter a:hover {
    color: #111;
}
/* -- Tagcloud -- */
.tagcloud {
    padding-top: 10px;
}
.widget .tagcloud a,
.widget.widget_product_tag_cloud a {
    display: inline-block;
    float: left;
    margin: 0 5px 5px 0;
    padding: 6px 10px;
    border-radius: 3px;
    color: #fff;
    background-color: #282828;
    font-size: 12px !important;
    line-height: 1;
    transition: all 0.2s;
}
.widget-area .widget .tagcloud a:hover,
.widget-area .widget.widget_product_tag_cloud a:hover {
    text-decoration: none;
    color: #fff;
}
/* -- Recent entries -- */
.widget.widget_recent_entries ul li {
    margin-bottom: 0.8em;
}
.widget.widget_recent_entries ul li:last-child {
    margin-bottom: 0;
}
.widget_recent_entries li span {
    display: block;
    font-size: 11px;
    text-transform: uppercase;
    margin-top: 3px;
    letter-spacing: 0.6px;
    opacity: 0.8;
}
/* -- Recent comments -- */
.widget_recent_comments ul li {
    margin-bottom: 0.6em;
}
.widget_recent_comments ul li:last-child {
    margin-bottom: 0;
}
/* -- Recent reviews -- */
.widget_recent_reviews .reviewer {
    font-size: 12px;
}
/* -- Search widget -- */
.widget_product_search form,
.widget_search form {
    position: relative;
}
.widget_product_search input,
.widget_search input {
    width: 100%;
}
.widget-area .commercekit-ajs-results {
    z-index: 2;
}
.widget-area .widget .commercekit-ajs-results a:hover {
    text-decoration: none;
}
.widget-area .commercekit-ajs-view-all-holder > a,
.widget-area .commercekit-ajs-view-all-holder > a:hover {
    color: #fff;
}
.site-search input[type="submit"],
.widget_product_search form button[type="submit"],
.widget_product_search form input[type="submit"],
.widget_search form button[type=submit],
.widget_search form input[type=submit] {
    clip: rect(1px 1px 1px 1px);
    position: absolute !important;
    right: 0px;
}
.widget_search .wp-block-search__label {
    display: none;
}
.widget_search .wp-block-search .wp-block-search__input {
    border-color: #eee;
}
.widget_search:before {
    display: none;
}
/* -- Text widget -- */
.widget p {
    margin-bottom: 1em;
    font-size: 14px;
    line-height: 1.5;
}
.widget p:last-child {
    margin-bottom: 0;
}
.widget h4 {
    font-size: 20px;
}
@media (max-width: 992px) {
    .widget-area .textwidget img {
        max-width: 40%;
    }
}
/*
Animation
========
*/
@keyframes rotate {
  100% {
    transform: rotate(360deg); }
}
@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
/*
Icons
========
*/
/* -- Caret Down -- */
.menu-primary-menu-container > ul > li.menu-item-has-children > a:after,
.menu-primary-menu-container > ul > li.menu-item-has-children.nolink > span:after,
.main-navigation ul.menu li.menu-item-has-children span.caret:after {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9L12 16L5 9' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9L12 16L5 9' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
/* -- Caret Right -- */
.main-navigation ul.menu > li:not(.full-width) ul li.menu-item-has-children > a:after {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
/* -- Empty Cart and Mini Cart -- */
.woocommerce-cart .cart-empty:before,
.dmrthema-mini-cart-wrap .woocommerce-mini-cart__empty-message:before {
	-webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" clip-rule="evenodd" viewBox="0 0 32 40"><path d="M6.783 25.703a2.264 2.264 0 0 0-2.262 2.262 2.263 2.263 0 1 0 2.262-2.262Zm11.4 0a2.264 2.264 0 0 0-2.262 2.262 2.263 2.263 0 1 0 2.262-2.262Zm-11.4 1a1.263 1.263 0 1 1-.001 2.525 1.263 1.263 0 0 1 .001-2.526Zm11.4 0a1.263 1.263 0 1 1-.001 2.525 1.263 1.263 0 0 1 .001-2.526Zm2.566-5.095v1.69a.39.39 0 0 1-.39.39H3.718a.5.5 0 0 0 0 1h16.641a1.39 1.39 0 0 0 1.39-1.39v-2.055l.005-.02 3.621-15.39H30a.5.5 0 0 0 0-1h-5.021a.5.5 0 0 0-.487.385l-1.056 4.49H2a.5.5 0 0 0-.493.583l1.863 10.9a.5.5 0 0 0 .493.417h16.886Zm2.452-10.9H2.593l1.691 9.9h16.587l2.33-9.9Zm-6.549-3.71 2.25-3.66a.501.501 0 0 0-.852-.523L15.8 6.474a.5.5 0 0 0 .852.524Zm-7.485-.524-2.25-3.66a.501.501 0 0 0-.852.525l2.25 3.659a.501.501 0 0 0 .852-.524Zm3.816-.541v-3.66a.5.5 0 0 0-1 0v3.66a.5.5 0 0 0 1 0Z"/></svg>');
	        mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" clip-rule="evenodd" viewBox="0 0 32 40"><path d="M6.783 25.703a2.264 2.264 0 0 0-2.262 2.262 2.263 2.263 0 1 0 2.262-2.262Zm11.4 0a2.264 2.264 0 0 0-2.262 2.262 2.263 2.263 0 1 0 2.262-2.262Zm-11.4 1a1.263 1.263 0 1 1-.001 2.525 1.263 1.263 0 0 1 .001-2.526Zm11.4 0a1.263 1.263 0 1 1-.001 2.525 1.263 1.263 0 0 1 .001-2.526Zm2.566-5.095v1.69a.39.39 0 0 1-.39.39H3.718a.5.5 0 0 0 0 1h16.641a1.39 1.39 0 0 0 1.39-1.39v-2.055l.005-.02 3.621-15.39H30a.5.5 0 0 0 0-1h-5.021a.5.5 0 0 0-.487.385l-1.056 4.49H2a.5.5 0 0 0-.493.583l1.863 10.9a.5.5 0 0 0 .493.417h16.886Zm2.452-10.9H2.593l1.691 9.9h16.587l2.33-9.9Zm-6.549-3.71 2.25-3.66a.501.501 0 0 0-.852-.523L15.8 6.474a.5.5 0 0 0 .852.524Zm-7.485-.524-2.25-3.66a.501.501 0 0 0-.852.525l2.25 3.659a.501.501 0 0 0 .852-.524Zm3.816-.541v-3.66a.5.5 0 0 0-1 0v3.66a.5.5 0 0 0 1 0Z"/></svg>');
}
/* -- Remove -- */
a.remove:before {
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'%3E%3Cpolyline points='3 6 5 6 21 6'%3E%3C/polyline%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'%3E%3C/path%3E%3Cline x1='10' y1='11' x2='10' y2='17'%3E%3C/line%3E%3Cline x1='14' y1='11' x2='14' y2='17'%3E%3C/line%3E%3C/svg%3E");
}
/* -- Loading Spinner -- */
.woocommerce-checkout .blockUI.blockOverlay:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3C!-- --%3E%3Csvg width='38' height='38' viewBox='0 0 38 38' xmlns='http://www.w3.org/2000/svg' stroke='%23fff'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(1 1)' stroke-width='2'%3E%3Ccircle stroke-opacity='.5' cx='18' cy='18' r='18'/%3E%3Cpath d='M36 18c0-9.94-8.06-18-18-18'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 18 18' to='360 18 18' dur='1s' repeatCount='indefinite'/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3C!-- --%3E%3Csvg width='38' height='38' viewBox='0 0 38 38' xmlns='http://www.w3.org/2000/svg' stroke='%23fff'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(1 1)' stroke-width='2'%3E%3Ccircle stroke-opacity='.5' cx='18' cy='18' r='18'/%3E%3Cpath d='M36 18c0-9.94-8.06-18-18-18'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 18 18' to='360 18 18' dur='1s' repeatCount='indefinite'/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
/* -- Confirmation Check -- */
.woocommerce-checkout p.woocommerce-thankyou-order-received:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
/* -- Blog arrow -- */
.flow article.post a.post-thumbnail:before,
.grid article.post a.post-thumbnail:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 5L21 12M21 12L14 19M21 12L3 12' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 5L21 12M21 12L14 19M21 12L3 12' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
a.remove:before,
.woocommerce-checkout .blockUI.blockOverlay:before,
.woocommerce-cart .cart-empty:before,
.dmrthema-mini-cart-wrap .woocommerce-mini-cart__empty-message:before,
.widget_shopping_cart .product_list_widget li a.remove:before,
.woocommerce-checkout p.woocommerce-thankyou-order-received:before,
.flow article.post a.post-thumbnail:before,
.grid article.post a.post-thumbnail:before,
.menu-primary-menu-container > ul > li.menu-item-has-children > a:after,
.main-navigation ul.menu > li:not(.full-width) ul li.menu-item-has-children > a:after,
.main-navigation ul.menu li.menu-item-has-children span.caret:after,
.menu-primary-menu-container > ul > li.menu-item-has-children.nolink > span:after {
	-webkit-mask-position: center;
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
	-webkit-mask-size: contain;
	        mask-size: contain;
}
/*
Keyboard navigation
========
*/
.keyboard-active :focus-visible,
.keyboard-active .input-text:focus-visible,
.keyboard-active input[type="email"]:focus-visible,
.keyboard-active input[type="password"]:focus-visible,
.keyboard-active input[type="search"]:focus-visible {
    outline: 0.2rem solid #2491ff;
    outline-offset: 0;
    border-color: transparent;
    box-shadow: none;
}
.keyboard-active select.orderby:focus-visible {
    outline-offset: 4px;
}
.keyboard-active .swiper-slide-imglink:focus-visible {
    outline-offset: -4px;
}
.keyboard-active input:focus,
.keyboard-active textarea:focus {
    border-color: transparent;
}
/*
Utility classes
========
*/
.sr-only,
.screen-reader-text {
    position: absolute;
    visibility: hidden;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
.clear {
    clear: both;
}
label.inline {
    display: inline !important;
    margin-left: 1em;
}
@media (max-width: 992px) {
    .hide-on-mobile {
        display: none !important;
    }
}
