/* demir Elementor Pro Styling */

.site .elementor-products-grid ul.products.elementor-grid li.product {
	padding-left: 15px;
	padding-right: 15px;
	padding-bottom: 40px;
}

.page-template-elementor_header_footer .site-content:after { 
	display: none;
}

.col-full.topbar-wrapper {
	border: none;
}

@media (min-width: 992px) {
    .col-full.main-header, .col-full-nav { 
    	padding-top: 0px;
    	padding-bottom: 0px;
    }
}
@media (max-width: 992px) {
    .main-header, .site-branding { 
        height: 0px;
    }
}

@media (min-width: 993px) {

	.elementor-products-grid ul.products {
		width: auto;
	}

	.elementor-element.elementor-products-grid ul.products li.product {
		width: 100%;
	}

	.elementor-element.elementor-products-grid ul.products li.product.ckit-hide-cta:not(.product-category) {
		padding-bottom: 0;
		margin-bottom: 30px;
	}
}

@media (max-width: 992px) {

	.elementor-element.elementor-products-grid ul.products {
		width: 100%;
    }

    .theme-demir.m-grid-2 .elementor-products-grid .commercekit-wishlist.mini {
        top: 15px;
        right: 30px;
    }

    .m-grid-2 ul.products.elementor-grid {
    	padding: 0;
    }

    /* If using a custom Elementor Pro header, ensure that the mobile "show filters" button and the sticky tabs don't have a gap */
    body:has(div[data-elementor-type=header]) .mobile-filter,
    body:has(div[data-elementor-type=header]) .commercekit-atc-sticky-tabs {
        top: -1px;
    }

}

/* PLP */
@media (min-width: 993px) {
	.products.elementor-grid {
		display: grid;
	}

	.products.elementor-grid.columns-4 {
		grid-template-columns: repeat(4, 1fr);
	}

	.products.elementor-grid.columns-3 {
		grid-template-columns: repeat(3, 1fr);
	}

	.products.elementor-grid.columns-2 {
		grid-template-columns: repeat(2, 1fr);
	}
}

.elementor-element.elementor-products-grid ul.products li.product {
	width: 100%;
}

ul.products.elementor-grid {
	margin-left: 0;
}

body.static-cta-buttons ul.products.elementor-grid li.product:not(.product-category) {
    margin-bottom: 0px;
}

body.static-cta-buttons ul.products.elementor-grid li.product .button {
	position: relative;
}

/* Elementor Pro PDP */
.single-product .product .elementor-widget-woocommerce-product-price .price {
	float: none;
}

.single-product .elementor-add-to-cart .cart .single_add_to_cart_button {
	float: none;
    width: inherit;
    height: inherit;
    line-height: inherit;
}

/* Prevent next/prev buttons flash on transition */
.single-product .swiper-slide {
	will-change: revert;
}

@media (max-width: 991px) {
	.m-grid-1 .elementor-grid-tablet-2 .elementor-grid,
	.m-grid-2 .elementor-grid-tablet-2 .elementor-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .m-grid-1 .elementor-grid-tablet-3 .elementor-grid,
    .m-grid-2 .elementor-grid-tablet-3 .elementor-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
	.m-grid-1 .elementor-grid-mobile-1 .elementor-grid,
	.m-grid-2 .elementor-grid-mobile-1 .elementor-grid {
		grid-template-columns: repeat(1, 1fr);
	}
	.m-grid-1 .elementor-grid-mobile-2 .elementor-grid,
	.m-grid-2 .elementor-grid-mobile-2 .elementor-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}
