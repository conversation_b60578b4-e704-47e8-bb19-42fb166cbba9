<?php
/**
 *
 * Main menu theme options
 *
 * @package CommerceGurus
 * @subpackage dmrthema
 */

// Main Menu.
$dmrthema_default_options = dmrthema_get_option_defaults();

// Display top bar.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_layout_top_bar_display',
		'label'       => esc_html__( 'Display top bar?', 'dmrthema' ),
		'description' => esc_html__( 'Enable or disable the top bar', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_top_bar',
		'default'     => $dmrthema_default_options['dmrthema_layout_top_bar_display'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Enable', 'dmrthema' ),
			'disable' => esc_html__( 'Disable', 'dmrthema' ),
		),
	)
);

// Show or hide top bar on mobile.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_layout_top_bar_mobile',
		'label'       => esc_html__( 'Hide top bar on mobile?', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_top_bar',
		'default'     => $dmrthema_default_options['dmrthema_layout_top_bar_mobile'],
		'priority'    => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_top_bar_display',
				'value'    => 'enable',
				'operator' => '==',
			),
		),
		'transport'   => 'refresh',
		'choices'     => array(
			'show'  => esc_html__( 'Show', 'dmrthema' ),
			'hide' => esc_html__( 'Hide', 'dmrthema' ),
		),
	)
);

// Top bar padding.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_top_bar_padding',
		'label'       => esc_html__( 'Top bar padding', 'dmrthema' ),
		'description' => esc_html__( 'Adjusts the top and bottom padding.', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_top_bar',
		'default'     => 8,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.top-bar .textwidget',
				'property' => 'padding-top',
				'units'    => 'px',
				'media_query' => '@media (min-width: 992px)',
			),
			array(
				'element'  => '.top-bar .textwidget',
				'property' => 'padding-bottom',
				'units'    => 'px',
				'media_query' => '@media (min-width: 992px)',
			),
		),
	)
);

// Top bar font size.
dmrthema_Kirki::add_field(
	'dmrthema_config',
	array(
		'type'     => 'typography',
		'settings' => 'dmrthema_top_bar_font_size',
		'label'    => esc_html__( 'Top bar font size', 'dmrthema' ),
		'section'  => 'dmrthema_header_section_top_bar',
		'default'  => array(
			'font-size'      => '14px',
		),
		'priority' => 10,
		'output'   => array(
			array(
				'element'  => '.top-bar',
				'property' => 'font-size',
			),
		),
	)
);


// Header Layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_header_layout',
		'label'       => esc_html__( 'Header Layout', 'dmrthema' ),
		'description' => esc_html__( 'Change the header layout', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => $dmrthema_default_options['dmrthema_header_layout'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'default'  => esc_html__( 'Logo / Search / Secondary', 'dmrthema' ),
			'header-5' => esc_html__( 'Logo / Search / Secondary / Cart', 'dmrthema' ),
			'header-2' => esc_html__( 'Search / Logo / Secondary', 'dmrthema' ),
			'header-3' => esc_html__( 'Secondary / Logo / Search', 'dmrthema' ),
			'header-4' => esc_html__( 'Logo / Navigation / Cart', 'dmrthema' ),			
		),
	)
);

// Header Layout Contained or Full Width
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_header_layout_container',
		'label'       => esc_html__( 'Header Container', 'dmrthema' ),
		'description' => esc_html__( 'Change the header container', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => $dmrthema_default_options['dmrthema_header_layout_container'],
		'priority'    => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
		'transport'   => 'refresh',
		'choices'     => array(
			'contained'  => esc_html__( 'Contained', 'dmrthema' ),
			'full-width-header' => esc_html__( 'Full width', 'dmrthema' ),
		),
	)
);


// Header Padding Top.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_header_top_padding',
		'label'       => esc_html__( 'Header Top Padding', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the header top padding', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => 30,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.col-full.main-header',
				'property'    => 'padding-top',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),

		),
	)
);

// Header Padding Bottom.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_header_bottom_padding',
		'label'       => esc_html__( 'Header Bottom Padding', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the header bottom padding', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => 30,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.col-full.main-header',
				'property'    => 'padding-bottom',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Header Height - Only for header-4 layout.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_header_height',
		'label'       => esc_html__( 'Header Height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the header height', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => 90,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.header-4 .header-4-container',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .menu-primary-menu-container > ul > li.nolink, .header-4 .search-trigger',
				'property'    => 'line-height',
				'units'       => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Display the search.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_layout_search_display',
		'label'       => esc_html__( 'Display the search?', 'dmrthema' ),
		'description' => esc_html__( 'Enable or disable the search. (Ajaxify your product search in CommerceKit!)', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => $dmrthema_default_options['dmrthema_layout_search_display'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Product Search', 'dmrthema' ),
			'advanced-woo-search'  => esc_html__( 'Advanced Woo Search Plugin', 'dmrthema' ),
			'ajax-search-wc'  => esc_html__( 'FiboSearch Plugin', 'dmrthema' ),
			'smart-search-pro'  => esc_html__( 'Smart Search PRO Plugin', 'dmrthema' ),
			'yith-search'  => esc_html__( 'YITH WooCommerce Ajax Search Plugin', 'dmrthema' ),
			'regular'  => esc_html__( 'Regular Search', 'dmrthema' ),
			'disable' => esc_html__( 'Disable', 'dmrthema' ),
		),
	)
);

// Search title. Only if header-4 is selected.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_layout_search_title',
		'label'     => esc_html__( 'Search modal title', 'dmrthema' ),
		'section'   => 'dmrthema_header_section_layout',
		'default'   => $dmrthema_default_options['dmrthema_layout_search_title'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'js_vars'   => array(
			array(
				'element'  => '.search-modal-heading',
				'function' => 'html',
			),
		),
	)
);

// Search style.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_layout_search_display_type',
		'label'    => esc_attr__( 'Search design', 'dmrthema' ),
		'section'  => 'dmrthema_header_section_layout',
		'default'  => $dmrthema_default_options['dmrthema_layout_search_display_type'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_search_display',
				'value'    => 'disable',
				'operator' => '!==',
			),
		),
		'choices'  => array(
			'default' => esc_attr__( 'Filled', 'dmrthema' ),
			'outline'  => esc_attr__( 'Outline', 'dmrthema' ),
		),
		'priority' => 10,
	)
);

// Display My Account icon on desktop.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'select',
		'settings'  => 'dmrthema_layout_myaccount_display',
		'label'     => esc_html__( 'Display account icon on desktop', 'dmrthema' ),
		'section'   => 'dmrthema_header_section_layout',
		'default'  => $dmrthema_default_options['dmrthema_layout_myaccount_display'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),

		),
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			),
		),
	)
);


// Navigation Height.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_navigation_height',
		'label'       => esc_html__( 'Navigation Height', 'dmrthema' ),
		'description' => esc_html__( 'Adjust the navigation height', 'dmrthema' ),
		'section'     => 'dmrthema_navigation_section_layout',
		'default'     => 60,
		'priority'    => 1,
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span, .site-header-cart, .logo-mark',
				'property' => 'line-height',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'  => '.site-header-cart, .menu-primary-menu-container > ul > li.menu-button',
				'property' => 'height',
				'units'    => 'px',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Enable hover intent.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_menu_hover_intent',
		'label'     => esc_html__( 'Enable hover intent', 'dmrthema' ),
		'description' => esc_html__( 'Tracks cursor movement to interpret when it is likely a user intended to hover over menu', 'dmrthema' ),
		'section'   => 'dmrthema_navigation_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Display menu descriptions
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_menu_display_description',
		'label'     => esc_html__( 'Display menu descriptions', 'dmrthema' ),
		'section'   => 'dmrthema_navigation_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Sticky Navigation.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_sticky_header',
		'label'       => esc_html__( 'Sticky Navigation', 'dmrthema' ),
		'description' => esc_html__( 'Stick the navigation on scroll', 'dmrthema' ),
		'section'     => 'dmrthema_header_section_layout',
		'default'     => $dmrthema_default_options['dmrthema_sticky_header'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'enable'  => esc_html__( 'Enable', 'dmrthema' ),
			'disable' => esc_html__( 'Disable', 'dmrthema' ),
		),
	)
);

// Mobile Sticky Header
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'select',
		'settings' => 'dmrthema_sticky_mobile_header',
		'label'    => esc_attr__( 'Mobile Sticky Header', 'dmrthema' ),
		'section'  => 'dmrthema_section_general_mobile_header',
		'default'  => $dmrthema_default_options['dmrthema_sticky_mobile_header'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'dmrthema' ),
			'disable'  => esc_attr__( 'Disable', 'dmrthema' ),

		),
		'priority' => 10,
	)
);


// Main Navigation Links Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_color',
		'label'     => esc_html__( 'Navigation links', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_color'],
		'priority'  => 10,
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'output'    => array(
			array(
				'element'     => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.main-navigation ul.menu > li.menu-item-has-children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.menu-primary-menu-container > ul > li > a, .menu-primary-menu-container > ul > li.nolink > span',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.main-navigation ul.menu > li.menu-item-has-children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Header 4 (One row) Navigation Links Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_color_header_4',
		'label'     => esc_html__( 'Navigation links', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_color_header_4'],
		'priority'  => 10,
		'active_callback'  => [
			[
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'output'    => array(
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .dmrthema-cart .cart-contents .amount, .header-4 .search-trigger, .header-4 .search-trigger:hover, .header-4 .search-trigger:focus, .dmrthema-myaccount a, .dmrthema-myaccount a:hover',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .main-navigation ul.menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.menu > li.page_item_has_children > a::after, .header-4 .main-navigation ul.nav-menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.nav-menu > li.page_item_has_children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.header-4 .menu-primary-menu-container > ul > li > a, .header-4 .dmrthema-cart .cart-contents .amount, .header-4 .search-trigger, .header-4 .search-trigger:hover, .header-4 .search-trigger:focus, .dmrthema-myaccount a, .dmrthema-myaccount a:hover',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
			array(
				'element'     => '.header-4 .main-navigation ul.menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.menu > li.page_item_has_children > a::after, .header-4 .main-navigation ul.nav-menu > li.menu-item-has-children > a::after, .header-4 .main-navigation ul.nav-menu > li.page_item_has_children > a::after',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Main Navigation Links Hover/Selected Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_color_hover',
		'label'     => esc_html__( 'Navigation links hover/selected', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_color_hover'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a span:before, .menu-primary-menu-container > ul > li.nolink > span:before',
				'property' => 'border-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.menu-primary-menu-container > ul > li > a span:before, .menu-primary-menu-container > ul > li.nolink > span:before',
				'property' => 'border-color',
			),
		),
	)
);

// Fade out other menu items on hover.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_navigation_color_other_hover',
		'label'       => esc_html__( 'Fade out other links on hover', 'dmrthema' ),
		'description' => esc_html__( 'Opacity (%).', 'dmrthema' ),
		'section'     => 'dmrthema_color_section_navigation',
		'default'     => 0.65,
		'priority'    => 1,
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'     => array(
			'min'  => 0,
			'max'  => 1,
			'step' => 0.01,
		),
		'output'      => array(
			array(
				'element'  => '.menu-primary-menu-container > ul.menu:hover > li > a',
				'property' => 'opacity',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);


dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_colors_navigation_heading_1',
		'section'  => 'dmrthema_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Dropdowns', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);


// Navigation Dropdown Background Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_dropdown_background',
		'label'     => esc_html__( 'Navigation dropdown background', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_dropdown_background'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul.sub-menu',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul.sub-menu',
				'function'    => 'css',
				'property'    => 'background-color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Navigation Dropdown Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_dropdown_color',
		'label'     => esc_html__( 'Navigation dropdown text', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_dropdown_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul li a, .main-navigation ul.nav-menu ul li a',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul li a, .main-navigation ul.nav-menu ul li a',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Main Navigation Dropdown Hover Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_navigation_dropdown_hover_color',
		'label'     => esc_html__( 'Navigation dropdown hover', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_navigation_dropdown_hover_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'     => '.main-navigation ul.menu ul li.menu-item:not(.menu-item-image):not(.heading) > a:hover',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'     => '.main-navigation ul.menu ul li.menu-item:not(.menu-item-image):not(.heading) > a:hover',
				'function'    => 'css',
				'property'    => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_colors_navigation_heading_2',
		'section'  => 'dmrthema_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Secondary Navigation', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);


// Secondary Navigation Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_secondary_navigation_color',
		'label'     => esc_html__( 'Secondary navigation color', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_secondary_navigation_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.secondary-navigation .menu a, .ri.menu-item:before, .fa.menu-item:before',
				'property' => 'color',
			),
			array(
				'element'  => '.secondary-navigation .icon-wrapper svg',
				'property' => 'stroke',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.secondary-navigation .menu a, .ri.menu-item:before, .fa.menu-item:before',
				'function' => 'css',
				'property' => 'color',
			),
			array(
				'element'  => '.secondary-navigation .icon-wrapper svg',
				'property' => 'stroke',
			),
		),
	)
);

dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'     => 'custom',
		'settings' => 'dmrthema_colors_navigation_heading_3',
		'section'  => 'dmrthema_color_section_navigation',
		'default'  => '<div class="kirki-separator" 
	style="margin: 10px -12px;
	padding: 12px 12px;
	color: #111;
	text-transform: uppercase;
	letter-spacing: 1px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
	background-color: #fff;
	cursor: default;">' . esc_html__( 'Cart', 'dmrthema' ) . '</div>',
		'priority' => 10,
	)
);

// Navigation Cart Icon Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_cart_icon_color',
		'label'     => esc_html__( 'Cart icon', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_cart_icon_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .count, .dmrthema-cart a.cart-contents .count:after',
				'property' => 'border-color',
			),
			array(
				'element'  => '.dmrthema-cart a.cart-contents .count, .dmrthema-cart-icon i',
				'property' => 'color',
			),
			array(
				'element'  => '.dmrthema-cart a.cart-contents:hover .count, .dmrthema-cart a.cart-contents:hover .count',
				'property' => 'background-color',
			),
			array(
				'element'  => '.dmrthema-cart-icon svg',
				'property' => 'stroke',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .count, .dmrthema-cart a.cart-contents .count:after',
				'function' => 'css',
				'property' => 'border-color',
			),
			array(
				'element'  => '.dmrthema-cart a.cart-contents .count, .dmrthema-cart-icon i',
				'property' => 'color',
			),
			array(
				'element'  => '.dmrthema-cart a.cart-contents:hover .count, .dmrthema-cart a.cart-contents:hover .count',
				'property' => 'background-color',
			),
			array(
				'element'  => '.dmrthema-cart-icon svg',
				'property' => 'stroke',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Navigation Cart Text Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_cart_color',
		'label'     => esc_html__( 'Cart text', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_cart_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.dmrthema-cart .cart-contents',
				'property' => 'color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.dmrthema-cart .cart-contents',
				'function' => 'css',
				'property' => 'color',
			),
		),
	)
);

// Navigation Cart Hover Text Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_cart_hover_color',
		'label'     => esc_html__( 'Cart text hover color', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_cart_hover_color'],
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents:hover .count',
				'property' => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents:hover .count',
				'function' => 'css',
				'property' => 'color',
				'media_query' => '@media (min-width: 993px)',
			),
		),
	)
);

// Cart Quantity Bubble Background Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_cart_bubble_background_color',
		'label'     => esc_html__( 'Cart quantity background color', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_cart_bubble_background_color'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_cart_icon',
				'value'    => 'basket',
				'operator' => '!==',
			),
		),
		'priority'  => 10,
		'output'    => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count',
				'property' => 'background-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count',
				'function' => 'css',
				'property' => 'background-color',
			),
		),
	)
);

// Cart Quantity Bubble Border Color.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'color',
		'settings'  => 'dmrthema_cart_bubble_border_color',
		'label'     => esc_html__( 'Cart quantity border color', 'dmrthema' ),
		'section'   => 'dmrthema_color_section_navigation',
		'default'   => $dmrthema_default_options['dmrthema_cart_bubble_border_color'],
		'active_callback'  => array(
			array(
				'setting'  => 'dmrthema_layout_woocommerce_cart_icon',
				'value'    => 'basket',
				'operator' => '!==',
			),
		),
		'priority'  => 11,
		'output'    => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count',
				'property' => 'border-color',
			),
		),
		'transport' => 'postMessage',
		'js_vars'   => array(
			array(
				'element'  => '.dmrthema-cart a.cart-contents .dmrthema-cart-icon .mini-count',
				'function' => 'css',
				'property' => 'border-color',
			),
		),
	)
);

// Display Cart in Menu.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_layout_woocommerce_display_cart',
		'label'     => esc_html__( 'Display cart', 'dmrthema' ),
		'section'   => 'dmrthema_cart_section_layout',
		'default'   => 1,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart Icon.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'select',
		'settings'    => 'dmrthema_layout_woocommerce_cart_icon',
		'label'       => esc_html__( 'Cart icon', 'dmrthema' ),
		'description' => esc_html__( 'If adjusting, test in an incognito window', 'dmrthema' ),
		'section'     => 'dmrthema_cart_section_layout',
		'default'     => $dmrthema_default_options['dmrthema_layout_woocommerce_cart_icon'],
		'priority'    => 10,
		'transport'   => 'refresh',
		'choices'     => array(
			'basket'  => esc_html__( 'Basket (Default)', 'dmrthema' ),
			'cart' => esc_html__( 'Cart icon', 'dmrthema' ),
			'bag' => esc_html__( 'Bag icon', 'dmrthema' ),
		),
	)
);

// Cart sidebar Title.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'text',
		'settings'  => 'dmrthema_cart_title',
		'label'     => esc_html__( 'Cart sidebar title', 'dmrthema' ),
		'section'   => 'dmrthema_cart_section_layout',
		'default'   => $dmrthema_default_options['dmrthema_cart_title'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.cart-drawer-heading',
				'function' => 'html',
			),
		),
	)
);

// Cart sidebar - display quantity.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_minicart_quantity',
		'label'     => esc_html__( 'Cart sidebar quantity arrows', 'dmrthema' ),
		'description' => esc_html__( 'Display quantity arrows in mini cart', 'dmrthema' ),
		'section'   => 'dmrthema_cart_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart sidebar - hide view cart link.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'toggle',
		'settings'  => 'dmrthema_sidebar_hide_cart_link',
		'label'     => esc_html__( 'Cart sidebar - hide "View Cart"', 'dmrthema' ),
		'section'   => 'dmrthema_cart_section_layout',
		'default'   => 0,
		'priority'  => 10,
		'transport' => 'refresh',
	)
);

// Cart sidebar below text.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'      => 'textarea',
		'settings'  => 'dmrthema_cart_below_text',
		'label'     => esc_html__( 'Cart sidebar below text', 'dmrthema' ),
		'section'   => 'dmrthema_cart_section_layout',
		'default'   => $dmrthema_default_options['dmrthema_cart_below_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'js_vars'   => array(
			array(
				'element'  => '.cart-drawer-below',
				'function' => 'html',
			),
		),
	)
);

// Below header padding.
dmrthema_Kirki::add_field(
	'dmrthema_config', array(
		'type'        => 'slider',
		'settings'    => 'dmrthema_below_header_padding',
		'label'       => esc_html__( 'Below header padding', 'dmrthema' ),
		'description' => esc_html__( 'Adjusts the top and bottom padding.', 'dmrthema' ),
		'section'     => 'dmrthema_below_header_section_layout',
		'default'     => 12,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 50,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'  => '.header-widget-region .widget',
				'property' => 'padding-top',
				'units'    => 'px',
			),
			array(
				'element'  => '.header-widget-region .widget',
				'property' => 'padding-bottom',
				'units'    => 'px',
			),
		),
	)
);

// Below header font size.
dmrthema_Kirki::add_field(
	'dmrthema_config',
	array(
		'type'     => 'typography',
		'settings' => 'dmrthema_below_header_font_size',
		'label'    => esc_html__( 'Below header font size', 'dmrthema' ),
		'section'  => 'dmrthema_below_header_section_layout',
		'default'  => array(
			'font-size'      => '14px',
		),
		'priority' => 10,
		'output'   => array(
			array(
				'element'  => '.header-widget-region',
				'property' => 'font-size',
			),
		),
	)
);


