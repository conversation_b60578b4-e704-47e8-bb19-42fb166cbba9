/*
WPForms
========
*/
.site-main div.wpforms-container-full .wpforms-form .wpforms-field-label {
	font-size: 15px;
	font-weight: 400;
}
dialog div.wpforms-container-full {
	margin-bottom: 0;
}
.site-main div.wpforms-container-full .wpforms-form .wpforms-title {
	font-weight: 600;
}
.site-content div.wpforms-container-full .wpforms-form select {
	width: 100%;
	max-width: 100%;
	height: 40px;
	margin-bottom: 5px;
	padding: 0 31px 0 11px;
	border: 1px solid #e2e2e2;
	border-radius: 3px;
	background: url("data:image/svg+xml;charset=utf8,%3Csvg width='1792' height='1792' xmlns='http://www.w3.org/2000/svg'%3E%3Cg%3E%3Ctitle%3Ebackground%3C/title%3E%3Crect fill='none' id='canvas_background' height='402' width='582' y='-1' x='-1'/%3E%3C/g%3E%3Cg%3E%3Ctitle%3ELayer 1%3C/title%3E%3Cpath fill='%23bfbfbf' id='svg_1' d='m1408,704q0,26 -19,45l-448,448q-19,19 -45,19t-45,-19l-448,-448q-19,-19 -19,-45t19,-45t45,-19l896,0q26,0 45,19t19,45z'/%3E%3C/g%3E%3C/svg%3E") calc(100% - 12px) 12px no-repeat;
	background-size: 15px 15px;
	box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
	font-size: 16px;
	font-weight: 400;
	line-height: 40px;
	text-indent: 0.01px;
	text-overflow: "";
	appearance: none;
	-webkit-font-smoothing: inherit;
}
.site div.wpforms-container-full .wpforms-form button[type="submit"] {
	padding: 12px 20px;
	border-radius: 4px;
	border: none;
	font-weight: 600;
	font-size: 15px;
	transition: all 0.2s;
	background-color: #323232;
	color: #fff;
}
.site div.wpforms-container-full .wpforms-form button[type="submit"]:hover {
	border: none;
	background-color: #111;
}
.site div.wpforms-container-full .wpforms-field.wpforms-field-select-style-classic select {
    padding-left: 10px;
}
@media only screen and (max-width: 600px) {
    div.wpforms-container-full .wpforms-form .wpforms-field:not(.wpforms-field-phone):not(.wpforms-field-select-style-modern):not(.wpforms-field-radio):not(.wpforms-field-checkbox):not(.wpforms-field-layout) {
        overflow-x: visible;
    }
}
/* -- Modals -- */
dialog div.wpforms-container-full .wpforms-form .wpforms-title {
	font-size: clamp(1.125rem, 0.8709rem + 0.813vw, 1.375rem); /* 18-22 */
}
div.wpforms-container-full .wpforms-form *:focus-visible {
	outline: 0.2rem solid #2491ff;
	outline-offset: -1px;
}
dialog div.wpforms-container-full .wpforms-form input:focus-visible,
dialog div.wpforms-container-full .wpforms-form select:focus-visible,
dialog div.wpforms-container-full .wpforms-form textarea:focus-visible {
	border-color: transparent;
}
dialog div.wpforms-container-full .wpforms-form button[type=submit]:focus {
	border: none;
}
dialog div.wpforms-container-full .wpforms-error-container {
	font-size: 14px;
    margin-top: -0.5rem;
    margin-bottom: 0.5rem;
}
