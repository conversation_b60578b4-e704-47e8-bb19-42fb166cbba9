document.addEventListener('DOMContentLoaded', function() {
  const dialogs = document.querySelectorAll('dialog.dmrthema-modal');
  var dmrthemaFElements = getFocusableElements();
  var dmrthemaFFElement = dmrthemaFElements[0];
  var dmrthemaLFElement = dmrthemaFElements[dmrthemaFElements.length - 1];
  
  document.addEventListener('click', event => {
    const dmrthematrigger = event.target.dataset.trigger;
    if (dmrthematrigger) {
      const modalId = dmrthematrigger;
      const modalElement = document.querySelector(`[data-dmrthemamodal-id="${modalId}"]`);
      if (modalElement) {
        closeAllDialogs();
        if (modalId === 'searchToggle') {
          modalElement.show();
          updateFocusableElements();
          trapSearchToggleModal(modalElement);
        } else {
          modalElement.showModal();
        }
      }
    }
  });
  
  dialogs.forEach(dmrthemadialog => {
    dmrthemadialog.addEventListener('click', function(event) {
      if (event.target === dmrthemadialog) {
        closeDialog(dmrthemadialog);
      }
      if (event.target.closest('.dmrthema-modal--button_close')) {
        event.preventDefault();
        closeDialog(dmrthemadialog);
      }
    });
    
    // Add keydown event listener for ESC key
    dmrthemadialog.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeDialog(dmrthemadialog);
      }
    });
  });
  
  function closeAllDialogs() {
    dialogs.forEach(dialog => {
      if (dialog.open) {
        dialog.close();
      }
    });
  }
  
  function closeDialog(dialog) {
    dialog.close();
  }
  
  function getFocusableElements() {
    var modalElm = document.querySelector('[data-dmrthemamodal-id="searchToggle"]');
    if (modalElm) {
      return modalElm.querySelectorAll('a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select');
    } else {
      return new Array();
    }
  }
  
  function updateFocusableElements() {
    dmrthemaFElements = getFocusableElements();
    dmrthemaFFElement = dmrthemaFElements[0];
    dmrthemaLFElement = dmrthemaFElements[dmrthemaFElements.length - 1];
  }
  
  function trapSearchToggleModal(element) {
    dmrthemaFFElement.focus();
    
    element.addEventListener('keydown', function(e) {
      let isTabPressed = e.key === 'Tab' || e.keyCode === 9;
      if (!isTabPressed) {
        return;
      }
      if (e.shiftKey) { 
        if (document.activeElement === dmrthemaFFElement) {
          dmrthemaLFElement.focus();
          e.preventDefault();
        }
      } else { 
        if (document.activeElement === dmrthemaLFElement) { 
          dmrthemaFFElement.focus(); 
          e.preventDefault();
        }
      }
    });
  }
  
  var modalContent = document.querySelector('[data-dmrthemamodal-id="searchToggle"]');
  if (modalContent) {
    var modalObserver = new MutationObserver(() => {
      updateFocusableElements();
    });
    modalObserver.observe(modalContent, { childList: true, subtree: true });
  }
});
