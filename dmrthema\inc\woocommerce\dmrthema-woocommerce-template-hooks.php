<?php
/**
 * dmrthema WooCommerce hooks
 *
 * @package dmrthema
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Styles
 *
 * @see  dmrthema_woocommerce_scripts()
 */

/**
 * Layout - Remove Default WooCommerce Actions
 * -----------------------------------------
 */
remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );
remove_action( 'woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10 );
remove_action( 'woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10 );
remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
remove_action( 'woocommerce_after_shop_loop', 'woocommerce_pagination', 10 );
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20 );
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30 );

/**
 * Global Layout Actions
 * -------------------
 */
add_action( 'woocommerce_before_main_content', 'dmrthema_before_content', 10 );
add_action( 'woocommerce_after_main_content', 'dmrthema_after_content', 10 );
add_action( 'dmrthema_content_top', 'dmrthema_shop_messages', 15 );
add_action( 'dmrthema_content_top', 'dmrthema_breadcrumbs', 5 );

/**
 * Product Loop Layout
 * ------------------
 */
// Sorting and Filtering
add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_wrapper', 9 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_wrapper_close', 31 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_product_columns_wrapper', 40 );

// Pagination and Results
add_action( 'woocommerce_before_shop_loop', 'dmrthema_woocommerce_pagination', 30 );
add_action( 'woocommerce_after_shop_loop', 'woocommerce_pagination', 30 );

// End Wrappers
add_action( 'woocommerce_after_shop_loop', 'dmrthema_sorting_wrapper_end', 9 );
add_action( 'woocommerce_after_shop_loop', 'dmrthema_sorting_wrapper_close', 31 );
add_action( 'woocommerce_after_shop_loop', 'dmrthema_product_columns_wrapper_close', 40 );

/**
 * Sorting and Results Count Display
 * -------------------------------
 */
$dmrthema_layout_woocommerce_display_sorting = dmrthema_get_option( 'dmrthema_layout_woocommerce_display_sorting' );
if ( true === $dmrthema_layout_woocommerce_display_sorting ) {
	add_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 10 );
	add_action( 'woocommerce_after_shop_loop', 'woocommerce_catalog_ordering', 10 );
}

$dmrthema_layout_woocommerce_display_count = dmrthema_get_option( 'dmrthema_layout_woocommerce_display_count' );
if ( true === $dmrthema_layout_woocommerce_display_count ) {
	add_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20 );
	add_action( 'woocommerce_after_shop_loop', 'woocommerce_result_count', 20 );
}

/**
 * Product Display Settings
 * ----------------------
 */
$dmrthema_layout_woocommerce_cta_display = dmrthema_get_option( 'dmrthema_layout_woocommerce_cta_display' );
if ( 'no-cta' === $dmrthema_layout_woocommerce_cta_display ) {
	remove_action( 'woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10 );
}

/**
 * Single Product Layout
 * -------------------
 */
remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40 );

$dmrthema_layout_woocommerce_meta_display = dmrthema_get_option( 'dmrthema_layout_woocommerce_meta_display' );
if ( true === $dmrthema_layout_woocommerce_meta_display ) {
	add_action( 'woocommerce_after_single_product_summary', 'woocommerce_template_single_meta', 15 );
}

/**
 * Related Products Display
 * ----------------------
 */
remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

$dmrthema_layout_woocommerce_related_display = dmrthema_get_option( 'dmrthema_layout_woocommerce_related_display' );
if ( true === $dmrthema_layout_woocommerce_related_display ) {
	add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
}

/**
 * Product Badge and Rating Display
 * -----------------------------
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_shop_out_of_stock', 8 );
remove_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5 );

$dmrthema_layout_woocommerce_display_badge = dmrthema_get_option( 'dmrthema_layout_woocommerce_display_badge' );
if ( true === $dmrthema_layout_woocommerce_display_badge ) {
	remove_action( 'woocommerce_before_shop_loop_item_title', 'woocommerce_show_product_loop_sale_flash', 10 );
}

$dmrthema_layout_woocommerce_display_rating = dmrthema_get_option( 'dmrthema_layout_woocommerce_display_rating' );
if ( true === $dmrthema_layout_woocommerce_display_rating ) {
	add_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 6 );
}

/**
 * Sticky Add to Cart
 * ----------------
 */
add_action( 'woocommerce_before_single_product_summary', 'dmrthema_sticky_single_add_to_cart', 30 );

/**
 * Header Cart and Search
 * --------------------
 */
$dmrthema_layout_woocommerce_enable_sidebar_cart = dmrthema_get_option( 'dmrthema_layout_woocommerce_enable_sidebar_cart' );
if ( true === $dmrthema_layout_woocommerce_enable_sidebar_cart ) {
	add_action( 'dmrthema_before_site', 'dmrthema_header_cart_drawer', 5 );
}

$dmrthema_layout_search_display = dmrthema_get_option( 'dmrthema_layout_search_display' );
if ( 'disable' !== $dmrthema_layout_search_display ) {
	add_action( 'dmrthema_header', 'dmrthema_product_search', 25 );
}

add_action( 'dmrthema_header', 'dmrthema_header_cart', 50 );
add_action( 'dmrthema_navigation', 'dmrthema_header_cart', 60 );

/**
 * Cart Fragment Support
 * -------------------
 */
if ( defined( 'WC_VERSION' ) && version_compare( WC_VERSION, '2.3', '>=' ) ) {
	add_filter( 'woocommerce_add_to_cart_fragments', 'dmrthema_cart_link_fragment' );
} else {
	add_filter( 'add_to_cart_fragments', 'dmrthema_cart_link_fragment' );
}

/**
 * Mobile Search Display
 * -------------------
 */
$dmrthema_search_mobile = dmrthema_get_option( 'dmrthema_search_mobile' );
$dmrthema_search_mobile_position = dmrthema_get_option( 'dmrthema_search_mobile_position' );

if ( 'enable' === $dmrthema_search_mobile ) {
	if ( 'within-navigation' === $dmrthema_search_mobile_position ) {
		add_action( 'dmrthema_navigation', 'dmrthema_product_search', 45 );
	}
}

/**
 * Checkout Coupon Position
 * ----------------------
 */
$dmrthema_checkout_coupon_position = dmrthema_get_option( 'dmrthema_checkout_coupon_position' );
if ( 'bottom' === $dmrthema_checkout_coupon_position ) {
	/**
	 * Checkout Page - Reorder the coupon code form so that it appears at the bottom of the page.
	 */
	remove_action( 'woocommerce_before_checkout_form', 'woocommerce_checkout_coupon_form', 10 );
	add_action( 'woocommerce_after_checkout_form', 'woocommerce_checkout_coupon_form' );
	add_action( 'woocommerce_after_checkout_form', 'dmrthema_coupon_wrapper_start', 5 );
	add_action( 'woocommerce_after_checkout_form', 'dmrthema_coupon_wrapper_end', 99 );
}

// Legacy WooCommerce columns filter.
if ( defined( 'WC_VERSION' ) && version_compare( WC_VERSION, '3.3', '<' ) ) {
	add_filter( 'loop_shop_columns', 'dmrthema_loop_columns' );
}

